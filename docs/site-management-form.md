# Site Management Page - T<PERSON>h năng Thêm mới / Cập nhật Site

## Tổng quan
Tính năng "Thêm mới / Cậ<PERSON> nhật" đã được thêm vào SiteManagementPage, cho phép người dùng tạo mới hoặc chỉnh sửa thông tin Site một cách dễ dàng.

## Cách sử dụng

### 1. <PERSON><PERSON><PERSON> cập tính năng
- Mở SiteManagementPage
- Trong Navigation Menu bên trái, click vào menu "Thêm mới / Cập nhật" (nằm dưới menu "Tổng quan")

### 2. Thêm Site mới
- Click vào menu "Thêm mới / Cập nhật"
- Form sẽ hiển thị với tiêu đề "Thêm mới Site"
- Điền các thông tin:
  - **Mã Site**: Bắt buộc nhập, tối đa 10 ký tự, chỉ chứa chữ hoa, số và dấu gạch dưới
  - **Tên Site**: B<PERSON><PERSON> buộc nhập, tối đa 250 ký tự
  - **<PERSON><PERSON> tả**: Kh<PERSON>ng bắt buộc, tối đa 1000 ký tự
  - **Trạng thái**: Mặc định là "Kích hoạt", có thể chuyển sang "Tạm dừng"
  - **Site cha**: Không bắt buộc, cho phép chọn từ danh sách sites hiện có

### 3. Cập nhật Site
- Từ trang "Danh sách", click vào nút "Chỉnh sửa" (icon bút chì) của site cần sửa
- Form sẽ hiển thị với tiêu đề "Cập nhật Site" và các thông tin hiện tại
- **Lưu ý**: Mã Site không thể chỉnh sửa khi đang cập nhật

### 4. Các nút thao tác
- **Lưu & Đóng**: Lưu dữ liệu và chuyển về trang "Danh sách"
- **Lưu & Tạo mới**: Lưu dữ liệu và reset form để nhập tiếp site mới
- **Hủy bỏ**: Không lưu và chuyển về trang "Danh sách"

## Validation và kiểm tra

### Validation tự động
- **Mã Site**: Kiểm tra format, độ dài và trùng lặp real-time
- **Tên Site**: Kiểm tra bắt buộc và độ dài
- **Mô tả**: Kiểm tra độ dài tối đa
- **Site cha**: Kiểm tra không được chọn chính nó làm parent

### Kiểm tra trùng lặp
- Hệ thống tự động kiểm tra mã Site đã tồn tại chưa
- Nếu trùng lặp, hiển thị thông báo lỗi và không cho phép lưu

## API Endpoints

### Backend APIs
- `GET /api/site-management/sites/check-code/{code}`: Kiểm tra mã Site trùng lặp
- `GET /api/site-management/sites/options`: Lấy danh sách sites cho dropdown
- `POST /api/site-management/sites`: Tạo site mới
- `PUT /api/site-management/sites/{id}`: Cập nhật site

### Frontend Features
- Real-time validation cho mã Site
- Search và filter cho dropdown Site cha
- Responsive design cho mobile và desktop
- Loading states và error handling

## Responsive Design
- Form tự động điều chỉnh layout trên mobile
- Các nút thao tác được sắp xếp phù hợp với màn hình nhỏ
- Input fields và validation hoạt động tốt trên touch devices

## Lưu ý kỹ thuật
- Sử dụng Ant Design Form với validation rules
- Async validation cho kiểm tra trùng lặp
- State management cho form data và loading states
- Error handling với user-friendly messages 