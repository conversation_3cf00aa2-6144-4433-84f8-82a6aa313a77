import React from 'react';
import { Form } from 'antd';
import SearchableDropdown from './SearchableDropdown';
import type { SearchableDropdownProps } from './SearchableDropdown';

export interface SearchableDropdownFormItemProps extends Omit<SearchableDropdownProps, 'value'> {
  label?: string;
  name: string;
  rules?: any[];
  required?: boolean;
  formItemStyle?: React.CSSProperties;
  tooltip?: string;
  help?: string;
  validateStatus?: 'success' | 'warning' | 'error' | 'validating';
  hasFeedback?: boolean;
}

const SearchableDropdownFormItem: React.FC<SearchableDropdownFormItemProps> = ({
  label,
  name,
  rules,
  required,
  formItemStyle,
  tooltip,
  help,
  validateStatus,
  hasFeedback,
  ...dropdownProps
}) => {
  return (
    <Form.Item
      label={label}
      rules={rules}
      required={required}
      style={formItemStyle}
      tooltip={tooltip}
      help={help}
      validateStatus={validateStatus}
      hasFeedback={hasFeedback}
    >
      <Form.Item name={name} noStyle>
        {({ value, onChange: formOnChange }: any) => {
          // Tìm option tương ứng với value hiện tại
          const currentOption = dropdownProps.options.find(opt => opt.value === value);
          const displayValue = currentOption ? currentOption.label : '';

          return (
            <SearchableDropdown
              {...dropdownProps}
              value={displayValue}
              onChange={(option) => {
                // Set giá trị vào form
                formOnChange(option ? option.value : undefined);

                // Gọi onChange callback nếu có
                dropdownProps.onChange?.(option);
              }}
              onClear={() => {
                // Clear giá trị trong form
                formOnChange(undefined);

                // Gọi onClear callback nếu có
                dropdownProps.onClear?.();
              }}
            />
          );
        }}
      </Form.Item>
    </Form.Item>
  );
};

export default SearchableDropdownFormItem;
