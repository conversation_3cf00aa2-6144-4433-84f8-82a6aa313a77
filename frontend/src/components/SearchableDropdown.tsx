import React, { useState, useEffect } from 'react';
import { Input } from 'antd';

export interface DropdownOption {
  value: number | string;
  label: string;
}

export interface SearchableDropdownProps {
  placeholder?: string;
  options: DropdownOption[];
  value?: string;
  onChange?: (option: DropdownOption | null) => void;
  onClear?: () => void;
  allowClear?: boolean;
  disabled?: boolean;
  loading?: boolean;
  style?: React.CSSProperties;
  dropdownStyle?: React.CSSProperties;
  notFoundContent?: string;
  autoComplete?: boolean;
  maxHeight?: number;
}

const SearchableDropdown: React.FC<SearchableDropdownProps> = ({
  placeholder = "Nhập để tìm kiếm...",
  options = [],
  value = '',
  onChange,
  onClear,
  allowClear = true,
  disabled = false,
  loading = false, // eslint-disable-line @typescript-eslint/no-unused-vars
  style,
  dropdownStyle,
  notFoundContent = "Không tìm th<PERSON>y kết quả phù hợp",
  autoComplete = true,
  maxHeight = 200
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [showDropdown, setShowDropdown] = useState(false);
  const [filteredOptions, setFilteredOptions] = useState<DropdownOption[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  // Sync external value changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Filter options based on input
  useEffect(() => {
    const inputStr = String(inputValue || '');
    if (inputStr.trim()) {
      const filtered = options.filter(option =>
        option.label.toLowerCase().includes(inputStr.toLowerCase())
      );
      setFilteredOptions(filtered);
    } else {
      setFilteredOptions(options);
    }
    setSelectedIndex(-1);
  }, [inputValue, options]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setSelectedIndex(-1);
    setShowDropdown(true);
  };

  const handleInputFocus = () => {
    setFilteredOptions(options);
    setSelectedIndex(-1);
    setShowDropdown(true);
  };

  const handleInputBlur = () => {
    // Delay để cho phép click vào option
    setTimeout(() => {
      setShowDropdown(false);
      setSelectedIndex(-1);
    }, 200);
  };

  const handleOptionClick = (option: DropdownOption) => {
    setInputValue(option.label);
    setShowDropdown(false);
    setSelectedIndex(-1);
    onChange?.(option);
  };

  const handleClear = () => {
    setInputValue('');
    setShowDropdown(false);
    setSelectedIndex(-1);
    onClear?.();
    onChange?.(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showDropdown || filteredOptions.length === 0) {
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredOptions.length - 1 ? prev + 1 : 0
        );
        break;
        
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : filteredOptions.length - 1
        );
        break;
        
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredOptions.length) {
          const selectedOption = filteredOptions[selectedIndex];
          handleOptionClick(selectedOption);
        }
        break;
        
      case 'Escape':
        e.preventDefault();
        setShowDropdown(false);
        setSelectedIndex(-1);
        break;
        
      case 'Tab':
        const inputStr = String(inputValue || '');
        if (autoComplete && filteredOptions.length > 0 && inputStr.trim()) {
          const firstOption = filteredOptions[0];
          // Auto complete nếu input match với đầu của option
          if (firstOption.label.toLowerCase().startsWith(inputStr.toLowerCase())) {
            setInputValue(firstOption.label);
            onChange?.(firstOption);
          }
        }
        setShowDropdown(false);
        setSelectedIndex(-1);
        break;
    }
  };

  return (
    <div style={{ position: 'relative', ...style }}>
      <Input
        placeholder={placeholder}
        value={inputValue}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onBlur={handleInputBlur}
        onKeyDown={handleKeyDown}
        allowClear={allowClear}
        onClear={handleClear}
        disabled={disabled}
        style={{ width: '100%' }}
        autoComplete="off"
      />
      
      {showDropdown && filteredOptions.length > 0 && (
        <div
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            backgroundColor: 'white',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: 1000,
            maxHeight: `${maxHeight}px`,
            overflowY: 'auto',
            ...dropdownStyle
          }}
        >
          {filteredOptions.map((option, index) => (
            <div
              key={`${option.value}-${index}`}
              onClick={() => handleOptionClick(option)}
              onMouseEnter={() => setSelectedIndex(index)}
              style={{
                padding: '8px 12px',
                cursor: 'pointer',
                borderBottom: index < filteredOptions.length - 1 ? '1px solid #f0f0f0' : 'none',
                backgroundColor: selectedIndex === index ? '#e6f7ff' : 'white',
                transition: 'background-color 0.2s'
              }}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
      
      {showDropdown && filteredOptions.length === 0 && String(inputValue || '').trim() && (
        <div
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            backgroundColor: 'white',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: 1000,
            padding: '8px 12px',
            color: '#999',
            textAlign: 'center',
            ...dropdownStyle
          }}
        >
          {notFoundContent}
        </div>
      )}
    </div>
  );
};

export default SearchableDropdown;
