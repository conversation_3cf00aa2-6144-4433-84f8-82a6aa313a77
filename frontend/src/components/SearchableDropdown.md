# SearchableDropdown Component

Một component dropdown có thể tìm kiếm với keyboard navigation, đư<PERSON><PERSON> thiết kế để thay thế cho Ant Design Select khi cần tránh các vấn đề về positioning và performance.

## Tính năng

- ✅ **Search real-time**: Filter ngay khi gõ
- ✅ **Keyboard navigation**: Hỗ trợ phím mũi tên, Enter, Tab, Escape
- ✅ **Auto complete**: Tab để tự động hoàn thành
- ✅ **Visual feedback**: Highlight option được chọn
- ✅ **Positioning chính xác**: Dropdown hiển thị đúng vị trí
- ✅ **Không giật màn hình**: Stable performance
- ✅ **TypeScript support**: Fully typed

## Keyboard Shortcuts

| Phím | Chức năng |
|------|-----------|
| `↓` | <PERSON> chuyển xuống option tiếp theo |
| `↑` | <PERSON> chuyển lên option trước đó |
| `Enter` | Chọn option được highlight |
| `Tab` | Auto complete với option đầu tiên |
| `Escape` | Đóng dropdown |

## Cách sử dụng

### 1. SearchableDropdown (Standalone)

```tsx
import { SearchableDropdown, DropdownOption } from '../components';

const options: DropdownOption[] = [
  { value: 1, label: "HQ - Trụ sở chính" },
  { value: 2, label: "BR1 - Chi nhánh 1" },
  { value: 3, label: "BR2 - Chi nhánh 2" }
];

function MyComponent() {
  const [selectedValue, setSelectedValue] = useState('');

  return (
    <SearchableDropdown
      placeholder="Chọn site..."
      options={options}
      value={selectedValue}
      onChange={(option) => {
        setSelectedValue(option ? option.label : '');
        console.log('Selected:', option);
      }}
      onClear={() => setSelectedValue('')}
      allowClear
      autoComplete
      maxHeight={200}
    />
  );
}
```

### 2. SearchableDropdownFormItem (Với Ant Design Form)

```tsx
import { Form } from 'antd';
import { SearchableDropdownFormItem, DropdownOption } from '../components';

const options: DropdownOption[] = [
  { value: 1, label: "HQ - Trụ sở chính" },
  { value: 2, label: "BR1 - Chi nhánh 1" }
];

function MyForm() {
  const [form] = Form.useForm();

  return (
    <Form form={form} layout="vertical">
      <SearchableDropdownFormItem
        label="Site cha"
        name="parent_id"
        placeholder="Chọn site cha..."
        options={options}
        allowClear
        autoComplete
        rules={[
          { required: true, message: 'Vui lòng chọn site cha!' }
        ]}
      />
    </Form>
  );
}
```

## Props

### SearchableDropdown Props

| Prop | Type | Default | Mô tả |
|------|------|---------|-------|
| `placeholder` | `string` | `"Nhập để tìm kiếm..."` | Placeholder text |
| `options` | `DropdownOption[]` | `[]` | Danh sách options |
| `value` | `string` | `''` | Giá trị hiển thị |
| `onChange` | `(option: DropdownOption \| null) => void` | - | Callback khi chọn option |
| `onClear` | `() => void` | - | Callback khi clear |
| `allowClear` | `boolean` | `true` | Cho phép clear |
| `disabled` | `boolean` | `false` | Disable component |
| `loading` | `boolean` | `false` | Hiển thị loading (reserved) |
| `style` | `React.CSSProperties` | - | Style cho container |
| `dropdownStyle` | `React.CSSProperties` | - | Style cho dropdown |
| `notFoundContent` | `string` | `"Không tìm thấy kết quả phù hợp"` | Text khi không có kết quả |
| `autoComplete` | `boolean` | `true` | Bật auto complete với Tab |
| `maxHeight` | `number` | `200` | Chiều cao tối đa của dropdown |

### SearchableDropdownFormItem Props

Kế thừa tất cả props của `SearchableDropdown` (trừ `value`) và thêm:

| Prop | Type | Default | Mô tả |
|------|------|---------|-------|
| `label` | `string` | - | Label của Form.Item |
| `name` | `string` | - | Name của field trong form |
| `rules` | `any[]` | - | Validation rules |
| `required` | `boolean` | - | Bắt buộc nhập |
| `formItemStyle` | `React.CSSProperties` | - | Style cho Form.Item |
| `tooltip` | `string` | - | Tooltip cho label |
| `help` | `string` | - | Help text |
| `validateStatus` | `'success' \| 'warning' \| 'error' \| 'validating'` | - | Validation status |
| `hasFeedback` | `boolean` | - | Hiển thị feedback icon |

### DropdownOption Interface

```tsx
interface DropdownOption {
  value: number | string;
  label: string;
}
```

## Ví dụ nâng cao

### Với API call

```tsx
function SiteSelector() {
  const [options, setOptions] = useState<DropdownOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchSites = async () => {
      setLoading(true);
      try {
        const response = await axios.get('/api/sites');
        const siteOptions = response.data.map(site => ({
          value: site.id,
          label: `${site.code} - ${site.name}`
        }));
        setOptions(siteOptions);
      } catch (error) {
        console.error('Error fetching sites:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSites();
  }, []);

  return (
    <SearchableDropdownFormItem
      label="Site"
      name="site_id"
      placeholder="Chọn site..."
      options={options}
      loading={loading}
      rules={[{ required: true, message: 'Vui lòng chọn site!' }]}
    />
  );
}
```

## Migration từ Ant Design Select

### Trước (Ant Design Select):
```tsx
<Form.Item label="Site cha" name="parent_id">
  <Select
    placeholder="Chọn Site cha"
    allowClear
    showSearch
    filterOption={(input, option) =>
      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
    }
    options={options}
  />
</Form.Item>
```

### Sau (SearchableDropdownFormItem):
```tsx
<SearchableDropdownFormItem
  label="Site cha"
  name="parent_id"
  placeholder="Chọn Site cha"
  options={options}
  allowClear
/>
```

## Lưu ý

1. **Performance**: Component này được tối ưu để tránh re-render không cần thiết
2. **Positioning**: Dropdown luôn hiển thị đúng vị trí, không bị lệch
3. **Accessibility**: Hỗ trợ đầy đủ keyboard navigation
4. **TypeScript**: Fully typed với IntelliSense support
5. **Responsive**: Hoạt động tốt trên mobile và desktop
