/* Reset và base styles cho full màn hình */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
  background: #f5f5f5;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Ant Design Layout overrides - chỉ override cần thiết */
.ant-layout {
  min-height: 100vh !important;
  background: #f5f5f5 !important;
}

/* Custom scrollbar cho modern look */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth transitions cho tất cả elements */
* {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-top {
  animation: slideInFromTop 0.4s ease-out;
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced hover effects */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 40px rgba(0,0,0,0.15) !important;
}

/* Card hover effects - chỉ override cần thiết */
.ant-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.ant-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

/* Button enhancements - chỉ override cần thiết */
.ant-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-weight: 500 !important;
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

/* Loading states với shimmer effect */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Custom card styles */
.modern-card {
  border-radius: 16px !important;
  border: none !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.modern-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 16px 48px rgba(0,0,0,0.16) !important;
}

/* Header gradient animation */
.header-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Dropdown fixes */
.ant-dropdown {
  z-index: 1050 !important;
}

.ant-dropdown-menu {
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
  border: none !important;
  padding: 8px 0 !important;
}

.ant-dropdown-menu-item {
  padding: 8px 16px !important;
  transition: background-color 0.2s ease !important;
  transform: none !important;
}

.ant-dropdown-menu-item:hover {
  background-color: #f5f5f5 !important;
  transform: none !important;
}

/* Disable global transitions for dropdown */
.ant-dropdown *,
.ant-dropdown-menu * {
  transition: none !important;
  animation: none !important;
}

/* Custom Tabs styling để căn giữa và cân đối */
.ant-tabs-nav {
  display: flex !important;
  justify-content: center !important;
  margin: 0 !important;
  padding: 0 24px !important;
  background: #fafafa !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.ant-tabs-nav-list {
  display: flex !important;
  justify-content: center !important;
  width: 100% !important;
  max-width: 600px !important;
}

.ant-tabs-tab {
  flex: 1 !important;
  max-width: 280px !important;
  min-width: 200px !important;
  text-align: center !important;
  margin: 0 !important;
  padding: 12px 16px !important;
  border-radius: 8px 8px 0 0 !important;
  border: 1px solid transparent !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
}

.ant-tabs-tab:hover {
  background: rgba(0, 0, 0, 0.02) !important;
  border-color: #d9d9d9 !important;
}

.ant-tabs-tab-active {
  background: #fff !important;
  border-color: #d9d9d9 !important;
  border-bottom-color: #fff !important;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06) !important;
}

.ant-tabs-tab-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.ant-tabs-content-holder {
  background: #fff !important;
  border-radius: 0 0 16px 16px !important;
}

.ant-tabs-tabpane {
  padding: 24px !important;
}

/* Responsive tabs */
@media (max-width: 768px) {
  .ant-tabs-nav-list {
    max-width: 100% !important;
  }
  
  .ant-tabs-tab {
    min-width: 150px !important;
    padding: 10px 12px !important;
  }
  
  .ant-tabs-tab-btn {
    font-size: 13px !important;
  }
}
