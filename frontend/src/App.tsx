import { ConfigProvider, App as AntdApp } from 'antd';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import AppsPage from './pages/apps/AppsPage';
import BasePage from './pages/apps/BasePage';
import UserManagementPage from './pages/apps/UserManagementPage';
import SiteManagementPage from './pages/apps/SiteManagementPage';
import './App.css'

// Theme tùy chỉnh hiện đại
const theme = {
  token: {
    colorPrimary: '#667eea',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#f5222d',
    colorInfo: '#1890ff',
    borderRadius: 8,
    wireframe: false,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
  },
  components: {
    Card: {
      borderRadiusLG: 16,
      boxShadowTertiary: '0 4px 12px rgba(0,0,0,0.08)',
    },
    Button: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Select: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Layout: {
      headerBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      bodyBg: '#f5f5f5',
    },
  },
};

function App() {
  return (
    <ConfigProvider theme={theme}>
      <AntdApp>
        <Router>
          <Routes>
            <Route path="/" element={<AppsPage />} />
            <Route path="/apps" element={<AppsPage />} />
            <Route path="/base" element={<BasePage />} />
            <Route path="/user-management" element={<UserManagementPage />} />
            <Route path="/site-management" element={<SiteManagementPage />} />
          </Routes>
        </Router>
      </AntdApp>
    </ConfigProvider>
  )
}

export default App
