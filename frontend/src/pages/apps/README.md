# Apps Pages - Metis Platform

## Tổng quan

Thư mục này chứa các trang quản lý ứng dụng (Apps) của Metis Platform, bao gồm AppsPage và BasePage.

## Cấu trúc

```
apps/
├── AppsPage.tsx          # Trang chính quản lý module
├── BasePage.tsx          # Trang chi tiết Base module
├── BasePage.css          # Styles cho BasePage
├── README.md             # Tài liệu này
├── CHANGELOG.md          # Lịch sử thay đổi
├── IMPLEMENTATION_SUMMARY.md  # Tóm tắt triển khai
└── demo.md               # Tài liệu demo
```

## AppsPage và BasePage

## Tổng quan

Ứng dụng quản lý module với hai trang chính:
- **AppsPage**: Trang chính hiển thị danh sách các module có thể cài đặt
- **BasePage**: Trang chi tiết cho Base Module, mở trong tab mới

## Tính năng chính

### AppsPage
- Hiển thị danh sách module theo tab (<PERSON><PERSON> thống / Phát triển)
- Chức năng cài đặt/gỡ cài đặt module
- Nút "View" mở BasePage trong tab mới
- Responsive design cho mobile và desktop

### BasePage
- **Mở trong tab mới**: Tăng tính độc lập giữa các module
- Hiển thị thông tin chi tiết Base Module
- Thống kê hệ thống và hoạt động gần đây
- Navigation linh hoạt (đóng tab hoặc quay về AppsPage)
- Auto-refresh khi tab được focus

## Cách sử dụng

### Mở BasePage từ AppsPage
1. Vào AppsPage (`/apps`)
2. Chọn tab "Module hệ thống"
3. Nhấn nút "View" (👁️) trên Base module
4. BasePage sẽ mở trong tab mới

### Điều hướng trong BasePage
- **Nút Back (←)**: 
  - Nếu mở từ tab khác → đóng tab hiện tại
  - Nếu không có opener → quay về AppsPage
- **Nút Apps (📱)**: Mở AppsPage trong tab mới
- **Nút Close (✕)**: Đóng tab hiện tại

## Cấu trúc file

```
src/pages/apps/
├── AppsPage.tsx          # Trang chính quản lý module
├── BasePage.tsx          # Trang chi tiết Base Module
├── BasePage.css          # Styles cho BasePage
├── README.md             # Documentation này
├── demo.md              # Hướng dẫn demo
└── IMPLEMENTATION_SUMMARY.md  # Tóm tắt implementation
```

## API Endpoints

### Backend
- `GET /api/modules` - Lấy danh sách module
- `POST /api/modules/{name}/install` - Cài đặt module
- `POST /api/modules/{name}/uninstall` - Gỡ cài đặt module
- `GET /api/internal/modules/{name}` - Lấy thông tin chi tiết module

### Frontend
- `/apps` - AppsPage
- `/base` - BasePage (mở trong tab mới)

## Responsive Design

### Desktop (>= 768px)
- Layout đầy đủ với sidebar và content
- Hiển thị tất cả thông tin chi tiết
- Navigation buttons đầy đủ

### Mobile (< 768px)
- Layout tối ưu cho màn hình nhỏ
- Collapsible sidebar
- Responsive typography và spacing
- Touch-friendly buttons

## Error Handling

- Axios interceptors cho network errors
- Graceful fallback khi API fails
- User-friendly error messages
- Debug logging cho development

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Tab management support
- LocalStorage cho state persistence
- Service worker handling

## Performance

- Lazy loading cho components
- Optimized re-renders
- Efficient API calls
- Minimal bundle size

## AppsPage

### Chức năng
- Hiển thị danh sách tất cả module trong hệ thống
- Phân loại module thành "Module Hệ thống" và "Module Phát triển"
- Cho phép cài đặt/gỡ cài đặt module
- Tìm kiếm và lọc module
- Quản lý cấu hình module

### Tính năng chính
- **Tab Module Hệ thống**: Hiển thị các Core module (is_core_module = true)
- **Tab Module Phát triển**: Hiển thị các module phát triển (is_core_module = false)
- **Nút View**: Mở trang chi tiết của module (hiện tại chỉ hỗ trợ Base module)
- **Nút Cài đặt/Gỡ cài đặt**: Quản lý trạng thái module
- **Advanced Filter**: Lọc module theo danh mục
- **Settings**: Xem cấu hình hệ thống

### API Endpoints sử dụng
- `GET /api/internal/modules/all` - Lấy danh sách module
- `POST /api/internal/modules/{name}/install` - Cài đặt module
- `POST /api/internal/modules/{name}/uninstall` - Gỡ cài đặt module
- `GET /api/internal/modules/config/all` - Lấy cấu hình module

## BasePage

### Chức năng
- Hiển thị thông tin chi tiết của Base module
- Dashboard với thống kê hệ thống
- Lịch sử hoạt động gần đây
- Navigation quay lại AppsPage

### Tính năng chính
- **Module Overview**: Thông tin tổng quan về Base module
- **System Statistics**: Thống kê bảng dữ liệu, API endpoints, người dùng, quyền hạn
- **Recent Activities**: Lịch sử hoạt động hệ thống
- **Responsive Design**: Tương thích với mobile và desktop
- **Sidebar Navigation**: Menu điều hướng (có thể mở rộng)

### API Endpoints sử dụng
- `GET /api/internal/modules/base` - Lấy thông tin chi tiết Base module

### Routing
- **URL**: `/base`
- **Navigation**: Từ AppsPage khi nhấn nút "View" trên Base module

## Cách sử dụng

### 1. Truy cập AppsPage
```
http://localhost:5173/apps
```

### 2. Xem Base Module
1. Mở AppsPage
2. Chuyển sang tab "Module Hệ thống"
3. Tìm Base module
4. Nhấn nút "View" (icon mắt)
5. Sẽ mở tab mới với BasePage

### 3. Quay lại AppsPage
- Từ BasePage: Nhấn nút mũi tên quay lại hoặc breadcrumb "Apps"

## Responsive Design

### Desktop (>= 1200px)
- Grid layout 4 cột cho module cards
- Sidebar đầy đủ cho BasePage
- Tất cả tính năng hiển thị

### Tablet (768px - 1199px)
- Grid layout 2-3 cột
- Sidebar có thể thu gọn
- Menu responsive

### Mobile (< 768px)
- Grid layout 1 cột
- Sidebar ẩn, sử dụng hamburger menu
- Touch-friendly buttons

## Error Handling

### Network Errors
- Hiển thị thông báo lỗi thân thiện
- Nút "Thử lại" để refresh
- Fallback data khi API không khả dụng

### Browser Extension Errors
- Tự động suppress lỗi browser extension
- Logging chi tiết cho debugging

## Development

### Cấu trúc Component
```typescript
interface Module {
  name: string;
  summary: string;
  version: string;
  state: 'installed' | 'uninstalled';
  author?: string;
  depends?: string[];
  category?: string;
  description?: string;
  is_core?: boolean;
  can_uninstall?: boolean;
  is_core_module?: boolean;
  auto_install?: boolean;
  hidden?: boolean;
}
```

### State Management
- Sử dụng React hooks (useState, useEffect, useCallback)
- Local state cho UI interactions
- API state cho data fetching

### Styling
- Ant Design components
- Custom CSS cho responsive design
- CSS modules cho component-specific styles

## Future Enhancements

### Planned Features
1. **Module Pages**: Tạo trang chi tiết cho tất cả module
2. **Real-time Updates**: WebSocket cho cập nhật real-time
3. **Module Analytics**: Thống kê sử dụng module
4. **Bulk Operations**: Cài đặt/gỡ cài đặt nhiều module cùng lúc
5. **Module Marketplace**: Tích hợp marketplace

### Technical Improvements
1. **Performance**: Lazy loading cho module pages
2. **Caching**: Cache API responses
3. **Offline Support**: Service worker cho offline mode
4. **Accessibility**: ARIA labels và keyboard navigation

## Troubleshooting

### Common Issues
1. **Module không hiển thị**: Kiểm tra API endpoint và database
2. **Lỗi kết nối**: Kiểm tra backend server
3. **Layout bị vỡ**: Kiểm tra responsive breakpoints

### Debug Commands
```bash
# Kiểm tra backend
curl http://localhost:8000/api/internal/modules/all

# Kiểm tra Base module
curl http://localhost:8000/api/internal/modules/base

# Kiểm tra frontend
curl http://localhost:5173
```

## Contributing

Khi thêm module page mới:
1. Tạo component trong thư mục `apps/`
2. Thêm route trong `App.tsx`
3. Cập nhật navigation logic trong `AppsPage.tsx`
4. Thêm API endpoint tương ứng trong backend
5. Cập nhật tài liệu này 