import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  Card,
  Button,
  Typography,
  Layout,
  Space,
  Avatar,
  Breadcrumb,
  Menu,
  Badge,
  Spin,
  message,
  Tag,
  Statistic,
  Table,
  Switch,
  Popconfirm,
  Tooltip,
  Input,
  Form,
  Select
} from 'antd';
import {
  InfoCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DatabaseOutlined,
  ApiOutlined,
  UserOutlined,
  SafetyOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  CloseOutlined,
  UnorderedListOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  PlusOutlined,
  SaveOutlined,
  FormOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { logError, getErrorMessage } from '../../utils/errorHandler';
import './SiteManagementPage.css';

const { Header, Content, Sider } = Layout;
const { Title, Text, Paragraph } = Typography;

interface SiteManagementModuleDetail {
  name: string;
  summary: string;
  version: string;
  state: 'installed' | 'uninstalled';
  author?: string;
  category?: string;
  description?: string;
  is_core_module?: boolean;
  auto_install?: boolean;
  hidden?: boolean;
  is_installed?: boolean;
}

interface SystemStats {
  tables: number;
  endpoints: number;
  users: number;
  permissions: number;
}

interface ActivityItem {
  time: string;
  description: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

interface Site {
  id: number;
  code: string;
  name: string;
  descriptions?: string;
  is_activated: boolean;
  parent_id?: number;
  children?: Site[];
}

const SiteManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(true);
  const [selectedKey, setSelectedKey] = useState('overview');
  const [moduleDetail, setModuleDetail] = useState<SiteManagementModuleDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [systemStats] = useState<SystemStats>({
    tables: 15,
    endpoints: 8,
    users: 3,
    permissions: 5
  });
  const [recentActivities] = useState<ActivityItem[]>([
    {
      time: '2 phút trước',
      description: 'Người dùng admin đã cập nhật cấu hình hệ thống',
      type: 'info'
    },
    {
      time: '15 phút trước',
      description: 'Hệ thống đã tạo bảng dữ liệu mới',
      type: 'success'
    },
    {
      time: '1 giờ trước',
      description: 'Module Site Management đã được cài đặt thành công',
      type: 'success'
    },
    {
      time: '2 giờ trước',
      description: 'Khởi tạo database schema',
      type: 'info'
    }
  ]);
  const [sites, setSites] = useState<Site[]>([]);
  const [sitesLoading, setSitesLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filteredSites, setFilteredSites] = useState<Site[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  
  // Form states
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [editingSiteId, setEditingSiteId] = useState<number | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [allSiteOptions, setAllSiteOptions] = useState<{ value: number; label: string }[]>([]);

  // Kiểm tra xem có phải tab mới không
  const isNewTab = window.opener || window.history.length <= 1;

  // Kiểm tra xem có phải mobile không
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Menu items
  const menuItems = [
    {
      key: 'overview',
      icon: <SettingOutlined />,
      label: 'Tổng quan',
    },
    {
      key: 'list',
      icon: <UnorderedListOutlined />,
      label: 'Danh sách',
    },
    {
      key: 'add',
      icon: <FormOutlined />,
      label: 'Thêm mới / Cập nhật',
    }
  ];

  // Fetch module details
  const fetchModuleDetails = async () => {
    try {
      console.log('DEBUG: Fetching site management module details...');
      const response = await axios.get('http://localhost:8000/api/internal/modules/site_management', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('DEBUG: Site management module details received:', response.data);
      
      const moduleData = response.data;
      setModuleDetail({
        name: moduleData.name || 'site_management',
        summary: moduleData.summary || 'Site Management Module',
        version: moduleData.version || '1.0',
        state: moduleData.is_installed ? 'installed' : 'uninstalled',
        author: moduleData.author || 'Metis AI',
        category: moduleData.category || 'Core',
        description: moduleData.description || 'Module quản lý thông tin và khai báo Site.',
        is_core_module: moduleData.is_core_module || true,
        auto_install: moduleData.auto_install || true,
        hidden: moduleData.hidden || false,
        is_installed: moduleData.is_installed || true
      });
    } catch (error: any) {
      logError('fetchModuleDetails', error);
      message.error('Không thể tải thông tin module Site Management');
      
      // Set default data if API fails
      setModuleDetail({
        name: 'site_management',
        summary: 'Site Management Module',
        version: '1.0',
        state: 'installed',
        author: 'Metis AI',
        category: 'Core',
        description: 'Module quản lý thông tin và khai báo Site.',
        is_core_module: true,
        auto_install: true,
        hidden: false,
        is_installed: true
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch sites data
  const fetchSites = async () => {
    try {
      setSitesLoading(true);
      console.log('DEBUG: Fetching sites data...');
      
      const response = await axios.get('http://localhost:8000/api/site-management/sites', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('DEBUG: Sites data received:', response.data);
      
      // Chuyển đổi dữ liệu phẳng thành cấu trúc cây
      const sitesData = response.data;
      const sitesMap = new Map<number, Site>();
      const rootSites: Site[] = [];

      // Tạo map cho tất cả sites
      sitesData.forEach((site: Site) => {
        sitesMap.set(site.id, { ...site, children: [] });
      });

      // Xây dựng cấu trúc cây
      sitesData.forEach((site: Site) => {
        const siteWithChildren = sitesMap.get(site.id)!;
        if (site.parent_id) {
          const parent = sitesMap.get(site.parent_id);
          if (parent) {
            parent.children!.push(siteWithChildren);
          }
        } else {
          rootSites.push(siteWithChildren);
        }
      });

      console.log('DEBUG: Tree structure built:', rootSites);
      setSites(rootSites);
      setFilteredSites(rootSites);

      // Tạo cache cho dropdown Site cha từ dữ liệu phẳng
      const allSiteOptionsCache = sitesData.map((site: Site) => ({
        value: site.id,
        label: `${site.code} - ${site.name}`
      }));
      setAllSiteOptions(allSiteOptionsCache);
      console.log('DEBUG: Created site options cache:', allSiteOptionsCache);

      // Tự động mở rộng tất cả các nhánh mặc định
      const allSiteIds = getAllSiteIds(rootSites);
      setExpandedRowKeys(allSiteIds);
    } catch (error: any) {
      logError('fetchSites', error);
      const errorMessage = getErrorMessage(error);
      message.error(`Không thể tải danh sách Sites: ${errorMessage}`);
      setSites([]);
      setFilteredSites([]);
      setExpandedRowKeys([]);
    } finally {
      setSitesLoading(false);
    }
  };

  // Hàm lấy tất cả ID của sites (bao gồm cả children)
  const getAllSiteIds = (sites: Site[]): React.Key[] => {
    const ids: React.Key[] = [];
    const collectIds = (siteList: Site[]) => {
      siteList.forEach(site => {
        ids.push(site.id);
        if (site.children && site.children.length > 0) {
          collectIds(site.children);
        }
      });
    };
    collectIds(sites);
    return ids;
  };

  // Filter sites based on search text
  const filterSites = (sites: Site[], searchText: string): Site[] => {
    if (!searchText.trim()) {
      return sites;
    }

    const searchLower = searchText.toLowerCase();
    
    const filterSite = (site: Site): boolean => {
      const matchesSearch = 
        site.code.toLowerCase().includes(searchLower) ||
        site.name.toLowerCase().includes(searchLower) ||
        (site.descriptions && site.descriptions.toLowerCase().includes(searchLower));
      
      // Check if any children match
      const childrenMatch = site.children && site.children.some(child => filterSite(child));
      
      return matchesSearch || !!childrenMatch;
    };

    const filterSiteWithChildren = (site: Site): Site | null => {
      const matchesSearch = 
        site.code.toLowerCase().includes(searchLower) ||
        site.name.toLowerCase().includes(searchLower) ||
        (site.descriptions && site.descriptions.toLowerCase().includes(searchLower));
      
      // Filter children recursively
      const filteredChildren = site.children 
        ? site.children.map(child => filterSiteWithChildren(child)).filter(Boolean) as Site[]
        : [];
      
      // Include site if it matches or has matching children
      if (matchesSearch || filteredChildren.length > 0) {
        return {
          ...site,
          children: filteredChildren
        };
      }
      
      return null;
    };

    const filtered = sites.map(site => filterSiteWithChildren(site)).filter(Boolean) as Site[];
    
    // Cập nhật expandedRowKeys khi filter
    const allFilteredIds = getAllSiteIds(filtered);
    setExpandedRowKeys(allFilteredIds);
    
    return filtered;
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchText(value);
    const filtered = filterSites(sites, value);
    setFilteredSites(filtered);
  };

  // Handle add new site
  const handleAddNewSite = () => {
    setIsEditing(false);
    setEditingSiteId(null);
    form.resetFields();
    form.setFieldsValue({
      is_activated: true,
      parent_id: undefined
    });
    setSelectedKey('add'); // Chuyển sang trang form
  };

  // Toggle site activation
  const handleToggleActivation = async (siteId: number, currentStatus: boolean) => {
    try {
      await axios.put(`http://localhost:8000/api/site-management/sites/${siteId}`, {
        is_activated: !currentStatus
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success('Cập nhật trạng thái thành công');
      fetchSites(); // Refresh data
    } catch (error: any) {
      logError('handleToggleActivation', error);
      message.error('Không thể cập nhật trạng thái');
    }
  };

  // Handle edit site
  const handleEditSite = (site: Site) => {
    setIsEditing(true);
    setEditingSiteId(site.id);
    form.setFieldsValue({
      code: site.code,
      name: site.name,
      descriptions: site.descriptions,
      is_activated: site.is_activated,
      parent_id: site.parent_id
    });
    setSelectedKey('add'); // Chuyển sang trang form
    // Parent options sẽ được load từ allSiteOptions trong dropdown
  };

  // Handle delete site
  const handleDeleteSite = async (siteId: number, siteName: string) => {
    try {
      await axios.delete(`http://localhost:8000/api/site-management/sites/${siteId}`);
      message.success(`Đã xóa Site "${siteName}" thành công`);
      fetchSites();
    } catch (error: any) {
      logError('handleDeleteSite', error);
      const errorMessage = getErrorMessage(error);
      message.error(`Không thể xóa Site: ${errorMessage}`);
    }
  };

  // Form handling functions
  const handleFormCancel = () => {
    form.resetFields();
    setSelectedKey('list');
  };

  const handleFormSave = async (action: 'save' | 'save_and_new') => {
    try {
      setFormLoading(true);
      const values = await form.validateFields();
      
      if (isEditing && editingSiteId) {
        // Cập nhật site
        await axios.put(`http://localhost:8000/api/site-management/sites/${editingSiteId}`, values);
        message.success('Cập nhật Site thành công');
      } else {
        // Tạo site mới
        await axios.post('http://localhost:8000/api/site-management/sites', values);
        message.success('Tạo Site mới thành công');
      }
      
      // Refresh danh sách sites
      await fetchSites();
      
      if (action === 'save') {
        // Lưu & Đóng - chuyển về trang danh sách
        form.resetFields();
        setSelectedKey('list');
      } else {
        // Lưu & Tạo mới - reset form để nhập tiếp
        form.resetFields();
        form.setFieldsValue({
          is_activated: true,
          parent_id: undefined
        });
        setIsEditing(false);
        setEditingSiteId(null);
      }
    } catch (error: any) {
      logError('handleFormSave', error);
      const errorMessage = getErrorMessage(error);
      message.error(`Không thể lưu Site: ${errorMessage}`);
    } finally {
      setFormLoading(false);
    }
  };





  const checkSiteCode = async (code: string): Promise<boolean> => {
    try {
      const params = new URLSearchParams();
      if (isEditing && editingSiteId) {
        params.append('site_id', editingSiteId.toString());
      }
      
      const response = await axios.get(
        `http://localhost:8000/api/site-management/sites/check-code/${code}?${params.toString()}`
      );
      
      return response.data.available;
    } catch (error) {
      console.error('Error checking site code:', error);
      return false;
    }
  };

  const validateSiteCode = async (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    
    if (!/^[A-Z0-9_]+$/.test(value)) {
      return Promise.reject(new Error('Mã Site chỉ được chứa chữ hoa, số và dấu gạch dưới!'));
    }
    
    if (value.length > 10) {
      return Promise.reject(new Error('Mã Site không được quá 10 ký tự!'));
    }
    
    const isAvailable = await checkSiteCode(value);
    if (!isAvailable) {
      return Promise.reject(new Error('Mã Site đã tồn tại!'));
    }
    
    return Promise.resolve();
  };

  useEffect(() => {
    fetchModuleDetails();
    
    // Fetch sites if list is selected
    if (selectedKey === 'list') {
      fetchSites();
    }
    
    // Fetch sites if add form is selected (for parent site options)
    if (selectedKey === 'add') {
      fetchSites();
    }
    
    // Cập nhật title của tab
    document.title = 'Site Management Module - Metis Platform';
    
    // Thêm event listener để refresh data khi tab được focus
    const handleFocus = () => {
      console.log('DEBUG: Tab focused, refreshing data...');
      fetchModuleDetails();
      if (selectedKey === 'list' || selectedKey === 'add') {
        fetchSites();
      }
    };
    
    // Thêm event listener để xử lý trước khi đóng tab
    const handleBeforeUnload = () => {
      // Có thể thêm logic để lưu trạng thái nếu cần
      console.log('DEBUG: Tab is being closed...');
    };
    
    // Xử lý resize window
    const handleResize = () => {
      const newIsMobile = window.innerWidth <= 768;
      setIsMobile(newIsMobile);
      
      // Luôn giữ collapsed = true cho cả mobile và desktop
      setCollapsed(true);
    };
    
    window.addEventListener('focus', handleFocus);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('resize', handleResize);
    };
  }, [selectedKey]);

  // Load parent options when sites are loaded - không cần nữa vì đã tự động load trong fetchSites

  const handleMenuClick = (e: any) => {
    setSelectedKey(e.key);
    if (e.key === 'add') {
      // Reset form khi chuyển sang trang thêm mới
      setIsEditing(false);
      setEditingSiteId(null);
      form.resetFields();
      form.setFieldsValue({
        is_activated: true,
        parent_id: undefined
      });
      // Parent options sẽ được load từ allSiteOptions trong dropdown
    }
  };

  const handleOpenAppsPage = () => {
    navigate('/apps');
  };

  const handleToggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const handleOverlayClick = () => {
    if (isMobile && !collapsed) {
      setCollapsed(true);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <ClockCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <InfoCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <ClockCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const renderContent = () => {
    switch (selectedKey) {
      case 'overview':
        return renderOverview();
      case 'list':
        return renderSitesList();
      case 'add':
        return renderSiteForm();
      default:
        return renderOverview();
    }
  };

  const renderOverview = () => {
    return (
      <div>
        {/* Module Overview Card */}
        <Card className="site-management-overview-card" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <Avatar 
                  size={80} 
                  icon={<SettingOutlined />} 
                  className="site-management-overview-avatar"
                  style={{ 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    fontSize: '32px'
                  }}
                />
                <Title level={3} style={{ margin: '16px 0 8px 0' }}>
                  {moduleDetail?.summary}
                </Title>
                <Text type="secondary" style={{ fontSize: '14px', fontFamily: 'monospace' }}>
                  {moduleDetail?.name}
                </Text>
              </div>
            </Col>
            <Col xs={24} md={16}>
              <div className="site-management-overview-stats">
                <Space size="large" wrap>
                  <div>
                    <Text type="secondary">Phiên bản</Text>
                    <div><Text strong>v{moduleDetail?.version}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">Tác giả</Text>
                    <div><Text strong>{moduleDetail?.author}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">Trạng thái</Text>
                    <div>
                      <Tag color={moduleDetail?.state === 'installed' ? 'green' : 'orange'}>
                        {moduleDetail?.state === 'installed' ? 'Đã cài đặt' : 'Chưa cài đặt'}
                      </Tag>
                    </div>
                  </div>
                </Space>
              </div>
              <div className="site-management-overview-tags" style={{ marginTop: '16px' }}>
                <Space wrap>
                  <Tag color="blue">{moduleDetail?.category}</Tag>
                  {moduleDetail?.is_core_module && (
                    <Tag color="red">Core Module</Tag>
                  )}
                  {moduleDetail?.auto_install && (
                    <Tag color="green">Tự động cài đặt</Tag>
                  )}
                </Space>
              </div>
              <Paragraph className="site-management-overview-description" style={{ marginTop: '16px' }}>
                {moduleDetail?.description}
              </Paragraph>
            </Col>
          </Row>
        </Card>

        {/* Statistics and Activities */}
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card title="Thống kê hệ thống" size="small" className="site-management-card-responsive">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="site-management-stats-card">
                    <Statistic
                      title="Bảng dữ liệu"
                      value={systemStats.tables}
                      valueStyle={{ color: '#1890ff', fontSize: '24px' }}
                      prefix={<DatabaseOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="site-management-stats-card">
                    <Statistic
                      title="API Endpoints"
                      value={systemStats.endpoints}
                      valueStyle={{ color: '#52c41a', fontSize: '24px' }}
                      prefix={<ApiOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="site-management-stats-card">
                    <Statistic
                      title="Người dùng"
                      value={systemStats.users}
                      valueStyle={{ color: '#faad14', fontSize: '24px' }}
                      prefix={<UserOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="site-management-stats-card">
                    <Statistic
                      title="Quyền hạn"
                      value={systemStats.permissions}
                      valueStyle={{ color: '#722ed1', fontSize: '24px' }}
                      prefix={<SafetyOutlined />}
                    />
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card title="Hoạt động gần đây" size="small" className="site-management-card-responsive">
              <div className="site-management-activity-card">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="site-management-activity-item">
                    <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                      {getActivityIcon(activity.type)}
                      <div style={{ flex: 1 }}>
                        <Text className="site-management-activity-time">{activity.time}</Text>
                        <div style={{ marginTop: '4px' }}>{activity.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  const renderSitesList = () => {
    const columns = [
      {
        title: 'Mã Site',
        dataIndex: 'code',
        key: 'code',
        width: 120,
        defaultSortOrder: 'ascend' as const,
        sorter: (a: Site, b: Site) => a.code.localeCompare(b.code),
        render: (text: string) => (
          <Text strong style={{ fontFamily: 'monospace', fontSize: '13px' }}>
            {text}
          </Text>
        ),
      },
      {
        title: 'Tên Site',
        dataIndex: 'name',
        key: 'name',
        sorter: (a: Site, b: Site) => a.name.localeCompare(b.name),
        render: (text: string) => (
          <Text strong style={{ fontSize: '14px' }}>{text}</Text>
        ),
      },
      {
        title: 'Mô tả',
        dataIndex: 'descriptions',
        key: 'descriptions',
        render: (text: string) => (
          <Text type="secondary" style={{ fontSize: '13px' }}>
            {text || 'Không có mô tả'}
          </Text>
        ),
      },
      {
        title: 'Trạng thái',
        dataIndex: 'is_activated',
        key: 'is_activated',
        width: 120,
        render: (isActivated: boolean, record: Site) => (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Switch
              checked={isActivated}
              onChange={() => handleToggleActivation(record.id, isActivated)}
              size="small"
            />
            <Tag color={isActivated ? 'green' : 'red'}>
              {isActivated ? 'Hoạt động' : 'Tạm dừng'}
            </Tag>
          </div>
        ),
      },
      {
        title: 'Thao tác',
        key: 'actions',
        width: 120,
        render: (_: any, record: Site) => (
          <Space size="small">
            <Tooltip title="Chỉnh sửa">
              <Button
                type="text"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEditSite(record)}
                style={{ color: '#1890ff' }}
              />
            </Tooltip>
            <Popconfirm
              title="Xóa Site"
              description={`Bạn có chắc chắn muốn xóa Site "${record.name}"?`}
              onConfirm={() => handleDeleteSite(record.id, record.name)}
              okText="Xóa"
              cancelText="Hủy"
              okType="danger"
            >
              <Tooltip title="Xóa">
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  size="small"
                  style={{ color: '#ff4d4f' }}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        ),
      },
    ];

    return (
      <div>
        <Card 
          title={
            <Space>
              <UnorderedListOutlined />
              <span>Danh sách Sites</span>
              <Tag color="blue">{filteredSites.length} sites</Tag>
            </Space>
          }
          extra={
            <Space className="site-management-action-buttons">
              <Input.Search
                placeholder="Tìm kiếm theo mã, tên hoặc mô tả..."
                allowClear
                style={{ width: 300 }}
                className="site-management-sites-search"
                onSearch={handleSearchChange}
                onChange={(e) => handleSearchChange(e.target.value)}
                value={searchText}
              />
              <Tooltip title="Làm mới">
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={fetchSites}
                  loading={sitesLoading}
                />
              </Tooltip>
              <Tooltip title="Thêm Site mới">
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={handleAddNewSite}
                />
              </Tooltip>
            </Space>
          }
          className="site-management-card-responsive"
        >
          {filteredSites.length === 0 && !sitesLoading ? (
            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
              <DatabaseOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
              <Text type="secondary">
                {searchText ? 'Không tìm thấy Sites phù hợp' : 'Không có dữ liệu Sites'}
              </Text>
              <br />
              <Button type="link" onClick={fetchSites} style={{ marginTop: '8px' }}>
                Thử lại
              </Button>
            </div>
          ) : (
            <Table
              columns={columns}
              dataSource={filteredSites}
              rowKey="id"
              loading={sitesLoading}
              className="site-management-sites-table"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} của ${total} sites`,
              }}
              expandable={{
                defaultExpandAllRows: true,
                expandedRowKeys: expandedRowKeys,
                onExpandedRowsChange: (expandedRows) => {
                  setExpandedRowKeys([...expandedRows]);
                },
                expandRowByClick: true,
                expandIcon: ({ expanded, onExpand, record }) => {
                  if (record.children && record.children.length > 0) {
                    return (
                      <Button
                        type="text"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          onExpand(record, e);
                        }}
                        style={{ padding: 0, width: 'auto' }}
                      >
                        {expanded ? '▼' : '▶'}
                      </Button>
                    );
                  }
                  return null;
                },
              }}
              scroll={{ x: 800 }}
              size="middle"
            />
          )}
        </Card>
      </div>
    );
  };

  const renderSiteForm = () => {
    return (
      <div>
        <Card 
          title={
            <Space>
              <FormOutlined />
              <span>{isEditing ? 'Cập nhật Site' : 'Thêm mới Site'}</span>
            </Space>
          }
          className="site-management-card-responsive"
        >
          <Form
            form={form}
            layout="vertical"
            className="site-management-form"
            initialValues={{
              is_activated: true
            }}
            onFinish={() => handleFormSave('save')}
          >
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Mã Site"
                  name="code"
                  rules={[
                    { required: true, message: 'Vui lòng nhập mã Site!' },
                    { validator: validateSiteCode }
                  ]}
                >
                  <Input 
                    placeholder="Nhập mã Site (VD: SITE001)"
                    disabled={isEditing}
                    style={{ fontFamily: 'monospace' }}
                  />
                </Form.Item>
              </Col>
              
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Tên Site"
                  name="name"
                  rules={[
                    { required: true, message: 'Vui lòng nhập tên Site!' },
                    { max: 250, message: 'Tên Site không được quá 250 ký tự!' }
                  ]}
                >
                  <Input placeholder="Nhập tên Site" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Mô tả"
                  name="descriptions"
                  rules={[
                    { max: 1000, message: 'Mô tả không được quá 1000 ký tự!' }
                  ]}
                >
                  <Input.TextArea 
                    placeholder="Nhập mô tả Site (không bắt buộc)"
                    rows={3}
                    showCount
                    maxLength={1000}
                  />
                </Form.Item>
              </Col>
              
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Trạng thái"
                  name="is_activated"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="Kích hoạt" 
                    unCheckedChildren="Tạm dừng"
                  />
                </Form.Item>
                
                <Form.Item
                  label="Site cha"
                  name="parent_id"
                >
                  <Select
                    placeholder="Chọn Site cha (không bắt buộc)"
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={allSiteOptions}
                    loading={sitesLoading}
                    notFoundContent="Không tìm thấy Site phù hợp"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                  <Space>
                    <Button onClick={handleFormCancel}>
                      Hủy bỏ
                    </Button>
                    <Button 
                      type="primary" 
                      icon={<SaveOutlined />}
                      loading={formLoading}
                      onClick={() => handleFormSave('save_and_new')}
                    >
                      Lưu & Tạo mới
                    </Button>
                    <Button 
                      type="primary" 
                      icon={<SaveOutlined />}
                      loading={formLoading}
                      onClick={() => handleFormSave('save')}
                    >
                      Lưu & Đóng
                    </Button>
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>
      </div>
    );
  };

  if (loading) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}>
          <div style={{ textAlign: 'center', color: 'white' }}>
            <Spin size="large" style={{ marginBottom: '16px' }} />
            <div style={{ fontSize: '24px', marginBottom: '16px' }}>Đang tải...</div>
            <div>Site Management Module</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout className="site-management-page-layout">
      {/* Header */}
      <Header className="site-management-page-header">
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          height: '100%'
        }}>
          <Space size="large">
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              {/* Icon menu cho cả mobile và desktop */}
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={handleToggleSidebar}
                style={{ 
                  color: 'white',
                  fontSize: '16px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
              <Avatar 
                size={32} 
                icon={<SettingOutlined />} 
                style={{ 
                  background: 'rgba(255,255,255,0.2)',
                  border: '1px solid rgba(255,255,255,0.3)',
                  fontSize: '16px'
                }}
              />
              <div>
                <Title level={5} className="site-management-title-responsive" style={{ color: 'white', margin: 0, fontSize: '16px' }}>
                  Site Management Module
                </Title>
                <Text className="site-management-subtitle-responsive" style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px', display: 'block' }}>
                  Quản lý thông tin và khai báo Site
                </Text>
              </div>
            </div>
          </Space>
          
          <Space>
            <Badge count={1} size="small">
              <Button 
                type="text" 
                icon={<InfoCircleOutlined />} 
                style={{ color: 'white' }}
                onClick={() => message.info('Module Site Management - Module quản lý thông tin và khai báo Site')}
              />
            </Badge>
            {isNewTab && (
              <>
                <Button 
                  type="text" 
                  icon={<AppstoreOutlined />} 
                  style={{ color: 'white' }}
                  onClick={handleOpenAppsPage}
                  title="Mở AppsPage trong tab mới"
                />
                <Button 
                  type="text" 
                  icon={<CloseOutlined />} 
                  style={{ color: 'white' }}
                  onClick={() => window.close()}
                  title="Đóng tab này"
                />
              </>
            )}
          </Space>
        </div>
      </Header>

      <Layout>
        {/* Sidebar Navigation */}
        <Sider 
          trigger={null} 
          collapsible 
          collapsed={collapsed}
          className={`site-management-page-sider ${collapsed ? 'collapsed' : ''}`}
          width={280}
          collapsedWidth={80}
          onClick={handleOverlayClick}
        >
          <Menu
            mode="inline"
            selectedKeys={[selectedKey]}
            items={menuItems}
            onClick={handleMenuClick}
            className="site-management-nav-menu"
          />
        </Sider>

        {/* Main Content */}
        <Layout className="site-management-page-content">
          <Content className="site-management-page-main-content" style={{ 
            margin: '24px',
            padding: '24px'
          }}>
            
            
            <Breadcrumb className="site-management-breadcrumb" style={{ marginBottom: '24px' }}>
              <Breadcrumb.Item>
                <Button 
                  type="link" 
                  onClick={handleOpenAppsPage}
                  style={{ padding: 0, height: 'auto', lineHeight: '1.5' }}
                >
                  Apps
                </Button>
              </Breadcrumb.Item>
              <Breadcrumb.Item>Site Management</Breadcrumb.Item>
              <Breadcrumb.Item>
                {selectedKey === 'overview' ? 'Tổng quan' : 
                 selectedKey === 'list' ? 'Danh sách' : 
                 selectedKey === 'add' ? (isEditing ? 'Cập nhật' : 'Thêm mới') : 'Tổng quan'}
              </Breadcrumb.Item>
            </Breadcrumb>
            
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default SiteManagementPage; 