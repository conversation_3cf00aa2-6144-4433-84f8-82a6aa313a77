/* BasePage.css - Responsive Design cho Base Module */

/* Mobile First Approach */
@media (max-width: 768px) {
  .base-page-header {
    padding: 0 16px !important;
    height: 56px !important;
  }
  
  .base-page-header .ant-typography {
    font-size: 14px !important;
  }
  
  .base-page-header .base-title-responsive {
    font-size: 14px !important;
    line-height: 1.2 !important;
  }
  
  .base-page-header .ant-avatar {
    width: 28px !important;
    height: 28px !important;
  }
  
  .base-page-sider {
    position: fixed !important;
    top: 56px !important;
    left: 0;
    z-index: 999;
    height: calc(100vh - 56px) !important;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 280px !important;
    background: #fff !important;
    box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
    padding-top: 0 !important;
  }
  
  /* Khi sidebar mở (collapsed = false) */
  .base-page-sider:not(.collapsed) {
    transform: translateX(0);
  }
  
  /* <PERSON>hi sidebar đóng (collapsed = true) */
  .base-page-sider.collapsed {
    transform: translateX(-100%);
  }
  
  .base-page-content {
    margin: 16px !important;
    padding: 16px !important;
    margin-left: 0 !important;
  }
  
  .base-page-main-content {
    margin: 16px !important;
    padding: 16px !important;
  }
  
  .base-page-breadcrumb {
    margin-bottom: 16px !important;
  }
  
  .base-overview-avatar {
    margin-bottom: 12px !important;
  }
  
  .base-overview-stats {
    margin-bottom: 12px !important;
  }
  
  .base-overview-stats .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .base-overview-stats .ant-space-item {
    margin-bottom: 8px !important;
  }
  
  /* Mobile menu toggle button */
  .base-nav-toggle {
    height: 48px !important;
    padding: 0 12px !important;
  }
  
  .base-nav-toggle .ant-btn {
    width: 32px !important;
    height: 32px !important;
    font-size: 14px !important;
  }
  
  .base-nav-toggle.collapsed {
    justify-content: center !important;
    padding: 0 !important;
  }
  
  .base-nav-toggle.expanded {
    justify-content: flex-start !important;
    padding: 0 12px !important;
  }
  
  /* Mobile overlay - chỉ hiển thị khi sidebar mở */
  .base-page-sider::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  /* Chỉ hiển thị overlay khi sidebar mở */
  .base-page-sider:not(.collapsed)::before {
    opacity: 1;
    pointer-events: auto;
  }
  
  /* Mobile menu items */
  .base-nav-menu .ant-menu-item {
    height: 48px !important;
    line-height: 48px !important;
    margin: 4px 8px !important;
    border-radius: 8px !important;
  }
  
  .base-nav-menu .ant-menu-item:hover {
    background: #f0f7ff !important;
    color: #1890ff !important;
  }
  
  .base-nav-menu .ant-menu-item-selected {
    background: #e6f7ff !important;
    color: #1890ff !important;
    font-weight: 600 !important;
  }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
  .base-page-header {
    padding: 0 20px !important;
  }
  
  .base-page-header .base-title-responsive {
    font-size: 15px !important;
    line-height: 1.3 !important;
  }
  
  .base-page-header .ant-avatar {
    width: 30px !important;
    height: 30px !important;
  }
  
  .base-page-content {
    margin: 20px !important;
    padding: 20px !important;
  }
  
  .base-page-sider {
    width: 240px !important;
  }
  
  .base-page-sider.collapsed {
    width: 80px !important;
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .base-page-sider {
    width: 280px !important;
  }
  
  .base-page-sider.collapsed {
    width: 80px !important;
  }
  
  .base-page-header .base-title-responsive {
    font-size: 16px !important;
    line-height: 1.4 !important;
  }
  
  .base-page-header .ant-avatar {
    width: 32px !important;
    height: 32px !important;
  }
}

/* Common Styles */
.base-page-layout {
  min-height: 100vh;
}

.base-page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 64px !important;
  padding: 0 24px !important;
}

.base-page-header .ant-typography {
  color: white !important;
  margin: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.base-page-header .ant-avatar {
  flex-shrink: 0;
}

.base-page-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0,0,0,0.1);
  position: sticky;
  top: 64px;
  height: calc(100vh - 64px);
  overflow: auto;
  padding-top: 0 !important;
}

.base-page-content {
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.base-page-main-content {
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 112px);
  overflow: auto;
  margin: 24px;
  padding: 24px;
}

.base-overview-card {
  margin-bottom: 24px;
}

.base-overview-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-bottom: 16px;
}

.base-overview-stats {
  margin-bottom: 16px;
}

.base-overview-tags {
  margin-bottom: 16px;
}

.base-overview-description {
  font-size: 14px;
  line-height: 1.6;
}

.base-stats-card {
  text-align: center;
}

.base-stats-number {
  font-size: 24px;
  font-weight: bold;
}

.base-stats-label {
  font-size: 12px;
  color: #666;
}

.base-activity-card {
  font-size: 12px;
}

.base-activity-item {
  margin-bottom: 8px;
}

.base-activity-time {
  color: #666;
  margin-bottom: 4px;
}

/* Navigation Menu Styles */
.base-nav-menu {
  border-right: 0;
  height: calc(100% - 65px);
  overflow: auto;
}

.base-nav-toggle {
  display: none !important;
}

/* Breadcrumb Styles */
.base-breadcrumb {
  margin-bottom: 24px;
  line-height: 1.5;
}

.base-breadcrumb .ant-breadcrumb-item {
  display: flex;
  align-items: center;
}

.base-breadcrumb .ant-breadcrumb-link {
  display: flex;
  align-items: center;
  height: auto;
  line-height: 1.5;
}

.base-breadcrumb .ant-btn {
  display: inline-flex;
  align-items: center;
  height: auto;
  line-height: 1.5;
  padding: 0;
  border: none;
  background: none;
  color: #1890ff;
  text-decoration: none;
}

.base-breadcrumb .ant-btn:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* Responsive Menu Toggle */
.mobile-menu-toggle {
  display: none;
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .desktop-menu-toggle {
    display: none;
  }
}

/* Content Area Responsive */
.base-content-area {
  transition: margin-left 0.3s ease;
}

@media (max-width: 768px) {
  .base-content-area {
    margin-left: 0 !important;
  }
  
  .base-content-area.sidebar-open {
    margin-left: 240px !important;
  }
}

/* Card Responsive */
.base-card-responsive {
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .base-card-responsive {
    margin-bottom: 12px;
  }
}

/* Button Responsive */
.base-button-responsive {
  height: 36px;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .base-button-responsive {
    height: 32px;
    font-size: 12px;
  }
}

/* Typography Responsive */
.base-title-responsive {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .base-title-responsive {
    font-size: 14px;
  }
}

.base-subtitle-responsive {
  font-size: 12px;
  color: rgba(255,255,255,0.9);
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .base-subtitle-responsive {
    font-size: 11px;
  }
}

/* Base Module Specific Styles */
.base-module-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.base-module-sider {
  border-right: 1px solid #f0f0f0;
}

.base-module-content {
  background: #fafafa;
}

.base-module-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.base-module-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  transition: box-shadow 0.3s ease;
}

/* System Configuration Styles */
.system-config-section {
  margin-bottom: 24px;
}

.system-config-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1890ff;
}

.system-config-item {
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
  margin-bottom: 8px;
}

/* User Management Styles */
.user-management-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* Data Management Styles */
.data-management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.data-management-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

/* Development Tools Styles */
.dev-tools-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.dev-tools-header {
  color: #722ed1;
  font-weight: 600;
  margin-bottom: 16px;
}

/* Documentation Styles */
.documentation-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}

.documentation-title {
  color: #52c41a;
  font-weight: 600;
  margin-bottom: 16px;
} 