import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  Card,
  Button,
  Typography,
  Layout,
  Space,
  Avatar,
  Breadcrumb,
  Menu,
  Badge,
  Spin,
  message,
  Tag,
  Statistic,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  InfoCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DatabaseOutlined,
  ApiOutlined,
  UserOutlined,
  SafetyOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { logError, getErrorMessage } from '../../utils/errorHandler';
import './UserManagementPage.css';

const { Header, Content, Sider } = Layout;
const { Title, Text, Paragraph } = Typography;

interface UserManagementModuleDetail {
  name: string;
  summary: string;
  version: string;
  state: 'installed' | 'uninstalled';
  author?: string;
  category?: string;
  description?: string;
  is_core_module?: boolean;
  auto_install?: boolean;
  hidden?: boolean;
  is_installed?: boolean;
}

interface UserStats {
  total_users: number;
  active_users: number;
  roles: number;
  permissions: number;
}

interface ActivityItem {
  time: string;
  description: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

const UserManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(true);
  const [selectedKey, setSelectedKey] = useState('overview');
  const [moduleDetail, setModuleDetail] = useState<UserManagementModuleDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [userStats, setUserStats] = useState<UserStats>({
    total_users: 12,
    active_users: 8,
    roles: 4,
    permissions: 15
  });
  const [recentActivities, setRecentActivities] = useState<ActivityItem[]>([
    {
      time: '5 phút trước',
      description: 'Người dùng mới "john.doe" đã được tạo',
      type: 'success'
    },
    {
      time: '15 phút trước',
      description: 'Role "Manager" đã được cập nhật quyền hạn',
      type: 'info'
    },
    {
      time: '1 giờ trước',
      description: 'Người dùng "admin" đã đăng nhập từ IP mới',
      type: 'warning'
    },
    {
      time: '2 giờ trước',
      description: 'Module User Management đã được cài đặt thành công',
      type: 'success'
    }
  ]);

  // Kiểm tra xem có phải tab mới không
  const isNewTab = window.opener || window.history.length <= 1;

  // Kiểm tra xem có phải mobile không
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Menu items
  const menuItems = [
    {
      key: 'overview',
      icon: <UserOutlined />,
      label: 'Tổng quan',
    }
  ];

  // Fetch module details
  const fetchModuleDetails = async () => {
    try {
      console.log('DEBUG: Fetching user management module details...');
      const response = await axios.get('http://localhost:8000/api/internal/modules/user_management', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('DEBUG: User management module details received:', response.data);
      
      const moduleData = response.data;
      setModuleDetail({
        name: moduleData.name || 'user_management',
        summary: moduleData.summary || 'User Management Module',
        version: moduleData.version || '1.0.0',
        state: moduleData.is_installed ? 'installed' : 'uninstalled',
        author: moduleData.author || 'Metis AI',
        category: moduleData.category || 'Core',
        description: moduleData.description || 'Module quản lý người dùng, vai trò và quyền hạn trong hệ thống Metis Platform',
        is_core_module: moduleData.is_core_module || true,
        auto_install: moduleData.auto_install || true,
        hidden: moduleData.hidden || false,
        is_installed: moduleData.is_installed || true
      });
    } catch (error: any) {
      logError('fetchModuleDetails', error);
      message.error('Không thể tải thông tin module User Management');
      
      // Set default data if API fails
      setModuleDetail({
        name: 'user_management',
        summary: 'User Management Module',
        version: '1.0.0',
        state: 'installed',
        author: 'Metis AI',
        category: 'Core',
        description: 'Module quản lý người dùng, vai trò và quyền hạn trong hệ thống Metis Platform',
        is_core_module: true,
        auto_install: true,
        hidden: false,
        is_installed: true
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModuleDetails();
    
    // Cập nhật title của tab
    document.title = 'User Management Module - Metis Platform';
    
    // Thêm event listener để refresh data khi tab được focus
    const handleFocus = () => {
      console.log('DEBUG: Tab focused, refreshing data...');
      fetchModuleDetails();
    };
    
    // Thêm event listener để xử lý trước khi đóng tab
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // Có thể thêm logic để lưu trạng thái nếu cần
      console.log('DEBUG: Tab is being closed...');
    };
    
    // Xử lý resize window
    const handleResize = () => {
      const newIsMobile = window.innerWidth <= 768;
      setIsMobile(newIsMobile);
      
      // Luôn giữ collapsed = true cho cả mobile và desktop
      setCollapsed(true);
    };
    
    window.addEventListener('focus', handleFocus);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleMenuClick = (e: any) => {
    setSelectedKey(e.key);
    // Trên mobile, đóng sidebar sau khi click menu
    if (isMobile) {
      setCollapsed(true);
    }
  };

  // Hàm xử lý navigation back
  const handleBackNavigation = () => {
    if (window.opener) {
      // Nếu mở từ tab khác, đóng tab hiện tại
      window.close();
    } else {
      // Nếu không có opener, navigate về AppsPage
      navigate('/apps');
    }
  };

  // Hàm mở AppsPage trong tab mới
  const handleOpenAppsPage = () => {
    window.open('/apps', '_blank');
  };

  // Hàm toggle sidebar
  const handleToggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  // Hàm đóng sidebar khi click overlay
  const handleOverlayClick = () => {
    if (!collapsed) {
      setCollapsed(true);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <ClockCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <ClockCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const renderContent = () => {
    return (
      <div>
        {/* Module Overview Card */}
        <Card className="user-management-overview-card" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <Avatar 
                  size={80} 
                  icon={<UserOutlined />} 
                  className="user-management-overview-avatar"
                  style={{ 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    fontSize: '32px'
                  }}
                />
                <Title level={3} style={{ margin: '16px 0 8px 0' }}>
                  {moduleDetail?.summary}
                </Title>
                <Text type="secondary" style={{ fontSize: '14px', fontFamily: 'monospace' }}>
                  {moduleDetail?.name}
                </Text>
              </div>
            </Col>
            <Col xs={24} md={16}>
              <div className="user-management-overview-stats">
                <Space size="large" wrap>
                  <div>
                    <Text type="secondary">Phiên bản</Text>
                    <div><Text strong>v{moduleDetail?.version}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">Tác giả</Text>
                    <div><Text strong>{moduleDetail?.author}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">Trạng thái</Text>
                    <div>
                      <Tag color={moduleDetail?.state === 'installed' ? 'green' : 'orange'}>
                        {moduleDetail?.state === 'installed' ? 'Đã cài đặt' : 'Chưa cài đặt'}
                      </Tag>
                    </div>
                  </div>
                </Space>
              </div>
              <div className="user-management-overview-tags" style={{ marginTop: '16px' }}>
                <Space wrap>
                  <Tag color="blue">{moduleDetail?.category}</Tag>
                  {moduleDetail?.is_core_module && (
                    <Tag color="red">Core Module</Tag>
                  )}
                  {moduleDetail?.auto_install && (
                    <Tag color="green">Tự động cài đặt</Tag>
                  )}
                </Space>
              </div>
              <Paragraph className="user-management-overview-description" style={{ marginTop: '16px' }}>
                {moduleDetail?.description}
              </Paragraph>
            </Col>
          </Row>
        </Card>

        {/* Statistics and Activities */}
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card title="Thống kê người dùng" size="small" className="user-management-card-responsive">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="user-management-stats-card">
                    <Statistic
                      title="Tổng người dùng"
                      value={userStats.total_users}
                      valueStyle={{ color: '#1890ff', fontSize: '24px' }}
                      prefix={<UserOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="user-management-stats-card">
                    <Statistic
                      title="Người dùng hoạt động"
                      value={userStats.active_users}
                      valueStyle={{ color: '#52c41a', fontSize: '24px' }}
                      prefix={<UserOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="user-management-stats-card">
                    <Statistic
                      title="Vai trò"
                      value={userStats.roles}
                      valueStyle={{ color: '#faad14', fontSize: '24px' }}
                      prefix={<SafetyOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="user-management-stats-card">
                    <Statistic
                      title="Quyền hạn"
                      value={userStats.permissions}
                      valueStyle={{ color: '#722ed1', fontSize: '24px' }}
                      prefix={<SafetyOutlined />}
                    />
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card title="Hoạt động gần đây" size="small" className="user-management-card-responsive">
              <div className="user-management-activity-card">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="user-management-activity-item">
                    <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                      {getActivityIcon(activity.type)}
                      <div style={{ flex: 1 }}>
                        <Text className="user-management-activity-time">{activity.time}</Text>
                        <div style={{ marginTop: '4px' }}>{activity.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  if (loading) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}>
          <div style={{ textAlign: 'center', color: 'white' }}>
            <Spin size="large" style={{ marginBottom: '16px' }} />
            <div style={{ fontSize: '24px', marginBottom: '16px' }}>Đang tải...</div>
            <div>User Management Module</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout className="user-management-page-layout">
      {/* Header */}
      <Header className="user-management-page-header">
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          height: '100%'
        }}>
          <Space size="large">
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              {/* Icon menu cho cả mobile và desktop */}
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={handleToggleSidebar}
                style={{ 
                  color: 'white',
                  fontSize: '16px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
              <Avatar 
                size={32} 
                icon={<UserOutlined />} 
                style={{ 
                  background: 'rgba(255,255,255,0.2)',
                  border: '1px solid rgba(255,255,255,0.3)',
                  fontSize: '16px'
                }}
              />
              <div>
                <Title level={5} className="user-management-title-responsive" style={{ color: 'white', margin: 0, fontSize: '16px' }}>
                  User Management Module
                </Title>
              </div>
            </div>
          </Space>
          
          <Space>
            <Badge count={1} size="small">
              <Button 
                type="text" 
                icon={<InfoCircleOutlined />} 
                style={{ color: 'white' }}
                onClick={() => message.info('User Management Module - Module quản lý người dùng và quyền hạn')}
              />
            </Badge>
            {isNewTab && (
              <>
                <Button 
                  type="text" 
                  icon={<AppstoreOutlined />} 
                  style={{ color: 'white' }}
                  onClick={handleOpenAppsPage}
                  title="Mở AppsPage trong tab mới"
                />
                <Button 
                  type="text" 
                  icon={<CloseOutlined />} 
                  style={{ color: 'white' }}
                  onClick={() => window.close()}
                  title="Đóng tab này"
                />
              </>
            )}
          </Space>
        </div>
      </Header>

      <Layout>
        {/* Sidebar Navigation */}
        <Sider 
          trigger={null} 
          collapsible 
          collapsed={collapsed}
          className={`user-management-page-sider ${collapsed ? 'collapsed' : ''}`}
          width={280}
          collapsedWidth={80}
          onClick={handleOverlayClick}
        >
          <Menu
            mode="inline"
            selectedKeys={[selectedKey]}
            items={menuItems}
            onClick={handleMenuClick}
            className="user-management-nav-menu"
            style={{
              border: 'none',
              background: 'transparent',
              padding: '16px 0'
            }}
          />
        </Sider>

        {/* Main Content */}
        <Layout className="user-management-page-content">
          <Content className="user-management-page-main-content" style={{ 
            margin: '24px',
            padding: '24px'
          }}>
            {/* Thông báo tab mới */}
            {isNewTab && (
              <Alert
                message="Tab mới"
                description="Bạn đang xem User Management Module trong tab mới. Tab này độc lập với AppsPage và có thể đóng riêng biệt."
                type="info"
                showIcon
                closable
                style={{ marginBottom: '16px' }}
              />
            )}
            
            <Breadcrumb className="user-management-breadcrumb" style={{ marginBottom: '24px' }}>
              <Breadcrumb.Item>
                <Button 
                  type="link" 
                  onClick={handleOpenAppsPage}
                  style={{ padding: 0, height: 'auto', lineHeight: '1.5' }}
                >
                  Apps
                </Button>
              </Breadcrumb.Item>
              <Breadcrumb.Item>User Management Module</Breadcrumb.Item>
              <Breadcrumb.Item>Tổng quan</Breadcrumb.Item>
            </Breadcrumb>
            
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default UserManagementPage; 