// Đường dẫn: frontend/src/utils/errorHandler.ts
// Utility để xử lý lỗi browser extension và network errors

export const handleBrowserExtensionError = () => {
  // Xử lý lỗi browser extension
  const handleRuntimeError = (event: ErrorEvent) => {
    if (event.message && event.message.includes('runtime.lastError')) {
      console.warn('Browser extension error detected, ignoring:', event.message);
      event.preventDefault();
      return false;
    }
  };

  const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    if (event.reason && event.reason.message && event.reason.message.includes('runtime.lastError')) {
      console.warn('Browser extension promise rejection detected, ignoring:', event.reason);
      event.preventDefault();
      return false;
    }
  };

  // Thêm event listeners
  window.addEventListener('error', handleRuntimeError);
  window.addEventListener('unhandledrejection', handleUnhandledRejection);

  // Return cleanup function
  return () => {
    window.removeEventListener('error', handleRuntimeError);
    window.removeEventListener('unhandledrejection', handleUnhandledRejection);
  };
};

export const getErrorMessage = (error: any): string => {
  if (error.code === 'ECONNREFUSED') {
    return 'Không thể kết nối đến backend server. Vui lòng kiểm tra xem backend có đang chạy không.';
  } else if (error.code === 'NETWORK_ERROR') {
    return 'Lỗi mạng. Vui lòng kiểm tra kết nối internet.';
  } else if (error.response?.status === 404) {
    return 'API endpoint không tồn tại. Vui lòng kiểm tra cấu hình backend.';
  } else if (error.response?.status >= 500) {
    return 'Lỗi server. Vui lòng kiểm tra logs backend.';
  } else if (error.message && error.message.includes('timeout')) {
    return 'Request timeout. Vui lòng thử lại sau.';
  } else if (error.message && error.message.includes('runtime.lastError')) {
    return 'Lỗi browser extension. Vui lòng tắt các extension và thử lại.';
  }
  
  return error.response?.data?.detail || error.message || 'Lỗi không xác định.';
};

export const logError = (context: string, error: any) => {
  console.error(`DEBUG: ${context} error:`, error);
  console.error(`DEBUG: ${context} error details:`, {
    message: error.message,
    code: error.code,
    response: error.response?.data,
    status: error.response?.status,
    config: error.config
  });
}; 