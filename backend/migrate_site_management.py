#!/usr/bin/env python3
"""
Migration script để tạo bảng Sites cho module site_management
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.database import engine, Base
from src.modules.site_management.models import Site

def create_site_management_tables():
    """Tạo bảng Sites cho module site_management"""
    try:
        print("Đang tạo bảng Sites...")
        
        # Tạo bảng Sites
        Site.__table__.create(engine, checkfirst=True)
        
        print("✅ Bảng Sites đã được tạo thành công!")
        
        # Thêm dữ liệu mẫu
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Kiểm tra xem đã có dữ liệu chưa
        existing_sites = session.query(Site).count()
        if existing_sites == 0:
            print("Đang thêm dữ liệu mẫu...")
            
            # Tạo Site cha
            parent_site = Site(
                code='HQ',
                name='Trụ sở chính',
                descriptions='Trụ sở chính của tổ chức',
                is_activated=True,
                parent_id=None
            )
            session.add(parent_site)
            session.commit()
            
            # Tạo Site con
            child_site = Site(
                code='BR1',
                name='Chi nhánh 1',
                descriptions='Chi nhánh đầu tiên của tổ chức',
                is_activated=True,
                parent_id=parent_site.id
            )
            session.add(child_site)
            session.commit()
            
            print("✅ Dữ liệu mẫu đã được thêm thành công!")
        else:
            print(f"ℹ️  Đã có {existing_sites} bản ghi Sites trong database")
        
        session.close()
        
    except Exception as e:
        print(f"❌ Lỗi khi tạo bảng Sites: {e}")
        return False
    
    return True

def drop_site_management_tables():
    """Xóa bảng Sites của module site_management"""
    try:
        print("Đang xóa bảng Sites...")
        
        # Xóa bảng Sites
        Site.__table__.drop(engine, checkfirst=True)
        
        print("✅ Bảng Sites đã được xóa thành công!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi xóa bảng Sites: {e}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Migration script cho module site_management')
    parser.add_argument('action', choices=['create', 'drop'], help='Hành động thực hiện')
    
    args = parser.parse_args()
    
    if args.action == 'create':
        success = create_site_management_tables()
        if success:
            print("\n🎉 Migration thành công!")
            sys.exit(0)
        else:
            print("\n💥 Migration thất bại!")
            sys.exit(1)
    elif args.action == 'drop':
        success = drop_site_management_tables()
        if success:
            print("\n🎉 Xóa bảng thành công!")
            sys.exit(0)
        else:
            print("\n💥 Xóa bảng thất bại!")
            sys.exit(1) 