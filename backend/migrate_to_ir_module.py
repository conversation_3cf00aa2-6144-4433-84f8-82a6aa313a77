#!/usr/bin/env python3
"""
Script để migrate từ bảng modules sang ir_module và xóa bảng modules
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.database import SessionLocal, engine
from src.modules.base.models import IrModule
from sqlalchemy import text

def migrate_to_ir_module():
    """Migrate dữ liệu từ bảng modules sang ir_module và xóa bảng modules"""
    
    print("🔄 Bắt đầu migration từ bảng modules sang ir_module...")
    
    db = SessionLocal()
    try:
        # Kiểm tra bảng modules có tồn tại không
        inspector = engine.dialect.inspector(engine)
        tables = inspector.get_table_names()
        
        if 'modules' not in tables:
            print("ℹ️  Bảng modules không tồn tại, không cần migration")
            return
        
        print("📊 Đang đọc dữ liệu từ bảng modules...")
        
        # Đ<PERSON><PERSON> dữ liệu từ bảng modules
        result = db.execute(text("SELECT module_name, is_installed, version, description FROM modules"))
        modules_data = result.fetchall()
        
        print(f"📋 Tìm thấy {len(modules_data)} module trong bảng modules")
        
        # Cập nhật trạng thái trong bảng ir_module
        for module_name, is_installed, version, description in modules_data:
            module_record = db.query(IrModule).filter(IrModule.name == module_name).first()
            
            if module_record:
                # Cập nhật trạng thái và thông tin
                module_record.state = 'installed' if is_installed else 'uninstalled'
                if version:
                    module_record.version = version
                if description:
                    module_record.description = description
                print(f"✅ Cập nhật module: {module_name} (state: {module_record.state})")
            else:
                # Tạo mới nếu chưa có
                new_module = IrModule(
                    name=module_name,
                    state='installed' if is_installed else 'uninstalled',
                    version=version or '1.0',
                    description=description or '',
                    summary='',
                    author='',
                    category='Other',
                    depends=[]
                )
                db.add(new_module)
                print(f"➕ Tạo mới module: {module_name} (state: {'installed' if is_installed else 'uninstalled'})")
        
        db.commit()
        print("✅ Đã cập nhật dữ liệu vào bảng ir_module")
        
        # Xóa bảng modules
        print("🗑️  Đang xóa bảng modules...")
        db.execute(text("DROP TABLE IF EXISTS modules CASCADE"))
        db.commit()
        print("✅ Đã xóa bảng modules")
        
        # Hiển thị kết quả
        print("\n📋 Dữ liệu hiện tại trong bảng ir_module:")
        modules = db.query(IrModule).all()
        for module in modules:
            print(f"  - {module.name}: {module.state} (v{module.version})")
        
        print("\n🎉 Migration hoàn thành thành công!")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Lỗi khi migration: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    migrate_to_ir_module() 