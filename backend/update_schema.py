#!/usr/bin/env python3
"""
Script để cập nhật database schema với các trường mới cho IrModule
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.database import SessionLocal, engine
from src.modules.base.models import IrModule, Base
from sqlalchemy import text

def update_schema():
    """Cập nhật database schema"""
    
    print("🔄 Bắt đầu cập nhật database schema...")
    
    # Tạo bảng mới nếu chưa tồn tại
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    try:
        # Kiểm tra xem có cần thêm cột mới không
        inspector = engine.dialect.inspector(engine)
        existing_columns = [col['name'] for col in inspector.get_columns('ir_module')]
        
        print(f"📊 Các cột hiện có: {existing_columns}")
        
        # Thêm cột depends nếu chưa có
        if 'depends' not in existing_columns:
            print("➕ Thêm cột 'depends'...")
            db.execute(text("ALTER TABLE ir_module ADD COLUMN depends VARCHAR(128)[] DEFAULT '{}'"))
            print("✅ Đã thêm cột 'depends'")
        
        # Thêm cột description nếu chưa có
        if 'description' not in existing_columns:
            print("➕ Thêm cột 'description'...")
            db.execute(text("ALTER TABLE ir_module ADD COLUMN description TEXT"))
            print("✅ Đã thêm cột 'description'")
        
        # Thêm cột category nếu chưa có
        if 'category' not in existing_columns:
            print("➕ Thêm cột 'category'...")
            db.execute(text("ALTER TABLE ir_module ADD COLUMN category VARCHAR(64)"))
            print("✅ Đã thêm cột 'category'")
        
        db.commit()
        print("🎉 Cập nhật schema thành công!")
        
        # Hiển thị thông tin schema mới
        inspector = engine.dialect.inspector(engine)
        new_columns = inspector.get_columns('ir_module')
        print("\n📋 Schema mới của bảng ir_module:")
        for col in new_columns:
            print(f"  - {col['name']}: {col['type']}")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Lỗi khi cập nhật schema: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    update_schema() 