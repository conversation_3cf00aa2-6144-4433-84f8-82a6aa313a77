# Đường dẫn: backend/src/main.py (<PERSON><PERSON><PERSON> bản <PERSON> x<PERSON>c)
import os
import json
import importlib.util # Import thư viện cần thiết

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .core.database import SessionLocal, engine
from .modules.base.models import IrModule
from .core import module_router # Import router quản lý module

app = FastAPI(title="Metis Platform")

# Cấu hình CORS
origins = [
    "http://localhost:3000",
    "http://localhost:5173",
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def sync_module_to_db(db_session, module_name, manifest_data):
    """Hàm này dùng để đồng bộ thông tin module vào DB."""
    module_record = db_session.query(IrModule).filter(IrModule.name == module_name).first()
    if not module_record:
        new_module = IrModule(
            name=module_name,
            version=manifest_data.get('version', '1.0'),
            summary=manifest_data.get('summary', ''),
            author=manifest_data.get('author', ''),
            description=manifest_data.get('description', ''),
            category=manifest_data.get('category', 'Other'),
            depends=manifest_data.get('depends', []),
            state='uninstalled'
        )
        db_session.add(new_module)
        print(f"INFO:     Registered new module: {module_name}")
    else:
        # Cập nhật thông tin nếu module đã tồn tại
        module_record.version = manifest_data.get('version', '1.0')
        module_record.summary = manifest_data.get('summary', '')
        module_record.author = manifest_data.get('author', '')
        module_record.description = manifest_data.get('description', '')
        module_record.category = manifest_data.get('category', 'Other')
        module_record.depends = manifest_data.get('depends', [])
        print(f"INFO:     Updated module: {module_name}")

@app.on_event("startup")
def on_startup():
    """Quét các module và đăng ký/cập nhật chúng vào database."""
    print("INFO:     Scanning for modules...")
    db = SessionLocal()
    try:
        module_path = os.path.join(os.path.dirname(__file__), 'modules')
        for module_name in os.listdir(module_path):
            manifest_path = os.path.join(module_path, module_name, '__manifest__.py')
            if os.path.isdir(os.path.join(module_path, module_name)) and os.path.exists(manifest_path):
                with open(manifest_path, 'r', encoding='utf-8') as f:
                    manifest_data = json.loads(f.read().replace("'", '"'))
                    sync_module_to_db(db, module_name, manifest_data)
        db.commit()
    except Exception as e:
        print(f"ERROR:    Error during module scan: {e}")
        db.rollback()
    finally:
        db.close()
    print("INFO:     Module scan finished.")
    
    # Tự động cài đặt module mặc định sử dụng logic mới
    module_router.auto_install_default_modules()

@app.get("/")
def read_root():
    return {"Hello": "Welcome to Metis Platform"}

# "Gắn" các API quản lý module vào ứng dụng chính
app.include_router(module_router.router)

# Đăng ký router của module site_management
from .modules.site_management.router import router as site_management_router
app.include_router(site_management_router)

# TODO: Thêm các router của các module khác vào đây khi chúng được cài đặt
# Ví dụ: app.include_router(user_router)