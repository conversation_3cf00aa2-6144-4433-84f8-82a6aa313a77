# Đường dẫn: backend/src/core/database.py
import os
import psycopg2
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base

# 1. Chuỗi kết nối (Connection String) đến PostgreSQL
# Lấy chuỗi kết nối từ biến môi trường, nếu không có thì dùng giá trị mặc định
# Đây là cách làm chuyên nghiệp để bảo mật mật khẩu
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "postgresql://postgres:Kata3003@localhost:5432/metisdb"
)

# 2. Tạo ra "Engine" - Cổng giao tiếp chính với database
# Engine quản lý một "pool" các kết nối để tối ưu hiệu năng.
engine = create_engine(DATABASE_URL)

# 3. Tạ<PERSON> ra một "Lớp Phiên làm việc" (Session Class)
# Mỗi khi cần thao tác với DB (th<PERSON><PERSON>, s<PERSON><PERSON>, x<PERSON><PERSON>, tru<PERSON> vấn), 
# chúng ta sẽ tạo một instance (phiên) từ lớp SessionLocal này.
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 4. Tạo ra một Lớp Base để các Model kế thừa
# Mọi class model (như User, Role...) mà chúng ta tạo ra để ánh xạ với bảng trong DB
# đều phải kế thừa từ lớp Base này. Đây là một yêu cầu của SQLAlchemy.
Base = declarative_base()

def get_db():
    """Dependency function để inject database session vào FastAPI endpoints"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db_connection():
    """Tạo kết nối PostgreSQL trực tiếp"""
    try:
        # Parse connection string
        if DATABASE_URL.startswith('postgresql://'):
            # Format: postgresql://username:password@host:port/database
            parts = DATABASE_URL.replace('postgresql://', '').split('@')
            user_pass = parts[0].split(':')
            host_db = parts[1].split('/')
            host_port = host_db[0].split(':')
            
            username = user_pass[0]
            password = user_pass[1]
            host = host_port[0]
            port = host_port[1] if len(host_port) > 1 else '5432'
            database = host_db[1]
            
            return psycopg2.connect(
                host=host,
                port=port,
                database=database,
                user=username,
                password=password
            )
        else:
            # Fallback to direct connection
            return psycopg2.connect(
                host='localhost',
                port='5432',
                database='metisdb',
                user='postgres',
                password='Kata3003'
            )
    except Exception as e:
        print(f"Error connecting to database: {e}")
        raise
