# Đường dẫn: backend/src/core/module_router.py
# Router quản lý module

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
import os
import importlib.util
from sqlalchemy.orm import Session
from .module_config import (
    get_auto_install_modules, 
    get_hidden_modules, 
    is_module_hidden,
    get_module_config,
    should_auto_install,
    is_core_module,
    can_uninstall_module,
    get_system_modules,
    get_development_modules
)
from .database import get_db, SessionLocal, engine
from ..modules.base.models import IrModule

router = APIRouter(prefix="/api/internal/modules", tags=["Internal Module Management"])

# Cache cho danh sách module đã quét
_discovered_modules = None

def scan_modules() -> List[str]:
    """Quét tất cả module có sẵn trong thư mục modules"""
    global _discovered_modules
    
    if _discovered_modules is not None:
        return _discovered_modules
    
    modules_dir = os.path.join(os.path.dirname(__file__), '..', 'modules')
    modules = []
    
    if os.path.exists(modules_dir):
        for item in os.listdir(modules_dir):
            item_path = os.path.join(modules_dir, item)
            if os.path.isdir(item_path):
                # Kiểm tra xem có file __init__.py không
                init_file = os.path.join(item_path, '__init__.py')
                if os.path.exists(init_file):
                    modules.append(item)
    
    _discovered_modules = modules
    return modules

def get_installed_modules(db: Session) -> List[str]:
    """Lấy danh sách module đã được cài đặt từ database sử dụng SQLAlchemy"""
    try:
        installed_modules = db.query(IrModule.name).filter(IrModule.state == 'installed').all()
        return [module[0] for module in installed_modules]
    except Exception as e:
        print(f"Error getting installed modules: {e}")
        return []

def install_module(module_name: str, db: Session) -> bool:
    """Cài đặt module vào database sử dụng SQLAlchemy"""
    try:
        # Kiểm tra module đã được cài đặt chưa
        module_record = db.query(IrModule).filter(IrModule.name == module_name).first()
        
        if module_record and module_record.state == 'installed':
            print(f"Module {module_name} is already installed")
            return True
        
        # Lấy cấu hình module
        config = get_module_config(module_name)
        
        if module_record:
            # Cập nhật trạng thái nếu module đã tồn tại
            module_record.state = 'installed'
            module_record.version = config.get('version', '1.0')
            module_record.description = config.get('description', '')
        else:
            # Tạo mới module record
            new_module = IrModule(
                name=module_name,
                state='installed',
                version=config.get('version', '1.0'),
                summary=config.get('summary', ''),
                author=config.get('author', ''),
                description=config.get('description', ''),
                category=config.get('category', 'Other'),
                depends=config.get('depends', [])
            )
            db.add(new_module)
        
        db.commit()
        
        # Thực hiện migration cho module cụ thể
        if module_name == 'site_management':
            try:
                from ..modules.site_management.models import Site
                Site.__table__.create(engine, checkfirst=True)
                print(f"Migration completed for module {module_name}")
            except Exception as migration_error:
                print(f"Migration error for module {module_name}: {migration_error}")
                # Không rollback vì module đã được đăng ký thành công
        
        print(f"Module {module_name} installed successfully")
        return True
        
    except Exception as e:
        db.rollback()
        print(f"Error installing module {module_name}: {e}")
        return False

def uninstall_module(module_name: str, db: Session) -> bool:
    """Gỡ cài đặt module khỏi database sử dụng SQLAlchemy"""
    try:
        # Kiểm tra xem có thể gỡ cài đặt không
        if not can_uninstall_module(module_name):
            print(f"Cannot uninstall core module {module_name}")
            return False
        
        # Tìm module record
        module_record = db.query(IrModule).filter(IrModule.name == module_name).first()
        
        if not module_record:
            print(f"Module {module_name} not found in database")
            return False
        
        if module_record.state != 'installed':
            print(f"Module {module_name} is not installed")
            return False
        
        # Thực hiện xóa bảng cho module cụ thể
        if module_name == 'site_management':
            try:
                from ..modules.site_management.models import Site
                Site.__table__.drop(engine, checkfirst=True)
                print(f"Tables dropped for module {module_name}")
            except Exception as drop_error:
                print(f"Error dropping tables for module {module_name}: {drop_error}")
                # Không rollback vì module vẫn cần được gỡ cài đặt
        
        # Cập nhật trạng thái
        module_record.state = 'uninstalled'
        db.commit()
        
        print(f"Module {module_name} uninstalled successfully")
        return True
        
    except Exception as e:
        db.rollback()
        print(f"Error uninstalling module {module_name}: {e}")
        return False

def auto_install_default_modules():
    """Tự động cài đặt các module mặc định"""
    print("Auto-installing default modules...")
    
    db = SessionLocal()
    try:
        # Lấy danh sách module cần tự động cài đặt
        auto_install_modules = []
        all_modules = scan_modules()
        
        for module_name in all_modules:
            if should_auto_install(module_name):
                auto_install_modules.append(module_name)
        
        # Cài đặt từng module
        for module_name in auto_install_modules:
            installed_modules = get_installed_modules(db)
            if module_name not in installed_modules:
                print(f"Installing module {module_name}")
                install_module(module_name, db)
            else:
                print(f"Module {module_name} is already installed")
        
        print("Auto-installation finished.")
    finally:
        db.close()

@router.get("/all")
async def get_all_modules(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Lấy tất cả module có sẵn và trạng thái cài đặt"""
    all_modules = scan_modules()
    installed_modules = get_installed_modules(db)
    
    modules_data = []
    for module_name in all_modules:
        # Bỏ qua module bị ẩn
        if is_module_hidden(module_name):
            continue
            
        config = get_module_config(module_name)
        is_installed = module_name in installed_modules
        
        modules_data.append({
            "name": module_name,
            "is_installed": is_installed,
            "is_core_module": config.get('is_core_module', False),
            "auto_install": config.get('auto_install', False),
            "hidden": config.get('hidden', False),
            "description": config.get('description', ''),
            "category": config.get('category', 'Other'),
            "author": config.get('author', ''),
            "version": config.get('version', '1.0'),
            "can_uninstall": can_uninstall_module(module_name)
        })
    
    return {
        "modules": modules_data,
        "total": len(modules_data)
    }

@router.get("/config/all")
async def get_all_module_configs() -> Dict[str, Any]:
    """Lấy cấu hình chi tiết của tất cả module"""
    all_modules = scan_modules()
    
    configs = {}
    for module_name in all_modules:
        config = get_module_config(module_name)
        configs[module_name] = {
            "module_name": module_name,
            "config": config
        }
    
    return configs

@router.get("/config/{module_name}")
async def get_module_config_api(module_name: str) -> Dict[str, Any]:
    """Lấy cấu hình chi tiết của một module cụ thể"""
    config = get_module_config(module_name)
    return {
        "module_name": module_name,
        "config": config
    }

@router.post("/install/{module_name}")
async def install_module_api(module_name: str, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Cài đặt module"""
    if module_name not in scan_modules():
        raise HTTPException(status_code=404, detail=f"Module {module_name} not found")
    
    success = install_module(module_name, db)
    if not success:
        raise HTTPException(status_code=500, detail=f"Failed to install module {module_name}")
    
    return {"message": f"Module {module_name} installed successfully"}

@router.post("/uninstall/{module_name}")
async def uninstall_module_api(module_name: str, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Gỡ cài đặt module"""
    if module_name not in scan_modules():
        raise HTTPException(status_code=404, detail=f"Module {module_name} not found")
    
    if not can_uninstall_module(module_name):
        raise HTTPException(status_code=400, detail=f"Cannot uninstall core module {module_name}")
    
    success = uninstall_module(module_name, db)
    if not success:
        raise HTTPException(status_code=500, detail=f"Failed to uninstall module {module_name}")
    
    return {"message": f"Module {module_name} uninstalled successfully"}

@router.get("/system")
async def get_system_modules_api(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Lấy danh sách module hệ thống (Core modules)"""
    system_modules = get_system_modules()
    installed_modules = get_installed_modules(db)
    
    modules_data = []
    for module_name in system_modules:
        config = get_module_config(module_name)
        is_installed = module_name in installed_modules
        
        modules_data.append({
            "name": module_name,
            "is_installed": is_installed,
            "description": config.get('description', ''),
            "category": config.get('category', 'Core'),
            "author": config.get('author', ''),
            "version": config.get('version', '1.0'),
            "can_uninstall": False  # Core modules không thể gỡ cài đặt
        })
    
    return {
        "modules": modules_data,
        "total": len(modules_data)
    }

@router.get("/development")
async def get_development_modules_api(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Lấy danh sách module phát triển"""
    development_modules = get_development_modules()
    installed_modules = get_installed_modules(db)
    
    modules_data = []
    for module_name in development_modules:
        config = get_module_config(module_name)
        is_installed = module_name in installed_modules
        
        modules_data.append({
            "name": module_name,
            "is_installed": is_installed,
            "description": config.get('description', ''),
            "category": config.get('category', 'Other'),
            "author": config.get('author', ''),
            "version": config.get('version', '1.0'),
            "can_uninstall": can_uninstall_module(module_name)
        })
    
    return {
        "modules": modules_data,
        "total": len(modules_data)
    }

@router.get("/{module_name}")
async def get_module_details_api(module_name: str, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Lấy thông tin chi tiết của một module cụ thể"""
    if module_name not in scan_modules():
        raise HTTPException(status_code=404, detail=f"Module {module_name} not found")
    
    # Lấy cấu hình module
    config = get_module_config(module_name)
    
    # Lấy trạng thái cài đặt từ database
    module_record = db.query(IrModule).filter(IrModule.name == module_name).first()
    is_installed = module_record.state == 'installed' if module_record else False
    
    # Tạo response data
    module_data = {
        "name": module_name,
        "summary": config.get('summary', module_name.replace('_', ' ').title()),
        "version": config.get('version', '1.0'),
        "is_installed": is_installed,
        "author": config.get('author', 'Metis AI'),
        "category": config.get('category', 'Other'),
        "description": config.get('description', ''),
        "is_core_module": is_core_module(module_name),
        "auto_install": config.get('auto_install', False),
        "hidden": config.get('hidden', False),
        "can_uninstall": can_uninstall_module(module_name),
        "depends": module_record.depends if module_record else []
    }
    
    return module_data