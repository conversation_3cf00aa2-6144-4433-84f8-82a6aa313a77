# Đường dẫn: backend/src/modules/site_management/router.py
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel

from ...core.database import get_db
from .models import Site

router = APIRouter(prefix="/api/site-management", tags=["Site Management"])

# Pydantic models cho request/response
class SiteBase(BaseModel):
    code: str
    name: str
    descriptions: Optional[str] = None
    is_activated: bool = True
    parent_id: Optional[int] = None

class SiteCreate(SiteBase):
    pass

class SiteUpdate(BaseModel):
    code: Optional[str] = None
    name: Optional[str] = None
    descriptions: Optional[str] = None
    is_activated: Optional[bool] = None
    parent_id: Optional[int] = None

class SiteResponse(SiteBase):
    id: int
    
    class Config:
        from_attributes = True

@router.get("/sites", response_model=List[SiteResponse])
def get_sites(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """L<PERSON>y danh sách tất cả Sites"""
    sites = db.query(Site).offset(skip).limit(limit).all()
    return sites

@router.get("/sites/options")
def get_site_options(db: Session = Depends(get_db)):
    """Lấy danh sách sites cho dropdown options"""
    sites = db.query(Site).filter(Site.is_activated == True).all()
    return [
        {
            "value": site.id,
            "label": f"{site.code} - {site.name}"
        }
        for site in sites
    ]

@router.get("/sites/check-code/{code}")
def check_site_code(code: str, site_id: Optional[int] = None, db: Session = Depends(get_db)):
    """Kiểm tra code Site có tồn tại không"""
    query = db.query(Site).filter(Site.code == code)
    
    # Nếu đang edit, loại trừ site hiện tại
    if site_id:
        query = query.filter(Site.id != site_id)
    
    existing_site = query.first()
    
    return {
        "code": code,
        "exists": existing_site is not None,
        "available": existing_site is None
    }

@router.get("/sites/{site_id}", response_model=SiteResponse)
def get_site(site_id: int, db: Session = Depends(get_db)):
    """Lấy thông tin Site theo ID"""
    site = db.query(Site).filter(Site.id == site_id).first()
    if site is None:
        raise HTTPException(status_code=404, detail="Site không tìm thấy")
    return site

@router.post("/sites", response_model=SiteResponse)
def create_site(site: SiteCreate, db: Session = Depends(get_db)):
    """Tạo Site mới"""
    # Kiểm tra code đã tồn tại chưa
    existing_site = db.query(Site).filter(Site.code == site.code).first()
    if existing_site:
        raise HTTPException(status_code=400, detail="Code đã tồn tại")
    
    # Kiểm tra parent_id có tồn tại không
    if site.parent_id:
        parent_site = db.query(Site).filter(Site.id == site.parent_id).first()
        if not parent_site:
            raise HTTPException(status_code=400, detail="Parent Site không tồn tại")
    
    db_site = Site(**site.dict())
    db.add(db_site)
    db.commit()
    db.refresh(db_site)
    return db_site

@router.put("/sites/{site_id}", response_model=SiteResponse)
def update_site(site_id: int, site_update: SiteUpdate, db: Session = Depends(get_db)):
    """Cập nhật thông tin Site"""
    db_site = db.query(Site).filter(Site.id == site_id).first()
    if db_site is None:
        raise HTTPException(status_code=404, detail="Site không tìm thấy")
    
    # Kiểm tra code mới có trùng với Site khác không
    if site_update.code and site_update.code != db_site.code:
        existing_site = db.query(Site).filter(Site.code == site_update.code).first()
        if existing_site:
            raise HTTPException(status_code=400, detail="Code đã tồn tại")
    
    # Kiểm tra parent_id có tồn tại không
    if site_update.parent_id:
        parent_site = db.query(Site).filter(Site.id == site_update.parent_id).first()
        if not parent_site:
            raise HTTPException(status_code=400, detail="Parent Site không tồn tại")
        
        # Kiểm tra không được set parent là chính nó
        if site_update.parent_id == site_id:
            raise HTTPException(status_code=400, detail="Không thể set parent là chính nó")
    
    # Cập nhật các trường
    update_data = site_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_site, field, value)
    
    db.commit()
    db.refresh(db_site)
    return db_site

@router.delete("/sites/{site_id}")
def delete_site(site_id: int, db: Session = Depends(get_db)):
    """Xóa Site"""
    db_site = db.query(Site).filter(Site.id == site_id).first()
    if db_site is None:
        raise HTTPException(status_code=404, detail="Site không tìm thấy")
    
    # Kiểm tra có Site con không
    children = db.query(Site).filter(Site.parent_id == site_id).count()
    if children > 0:
        raise HTTPException(status_code=400, detail="Không thể xóa Site có Site con")
    
    db.delete(db_site)
    db.commit()
    return {"message": "Site đã được xóa thành công"}

@router.get("/sites/{site_id}/children", response_model=List[SiteResponse])
def get_site_children(site_id: int, db: Session = Depends(get_db)):
    """Lấy danh sách Site con"""
    children = db.query(Site).filter(Site.parent_id == site_id).all()
    return children

@router.get("/sites/{site_id}/parent", response_model=SiteResponse)
def get_site_parent(site_id: int, db: Session = Depends(get_db)):
    """Lấy thông tin Site cha"""
    site = db.query(Site).filter(Site.id == site_id).first()
    if site is None:
        raise HTTPException(status_code=404, detail="Site không tìm thấy")
    
    if site.parent_id is None:
        raise HTTPException(status_code=404, detail="Site này không có Site cha")
    
    parent = db.query(Site).filter(Site.id == site.parent_id).first()
    if parent is None:
        raise HTTPException(status_code=404, detail="Site cha không tìm thấy")
    
    return parent 