from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from ...core.database import Base

class Site(Base):
    __tablename__ = "sites"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(10), nullable=False, unique=True, index=True)
    name = Column(String(250), nullable=False)
    descriptions = Column(String(1000), nullable=True)
    is_activated = Column(Boolean, default=True)
    parent_id = Column(Integer, ForeignKey('sites.id'), nullable=True)
    
    # Relationship với ch<PERSON>h nó (parent-child)
    parent = relationship("Site", remote_side=[id], backref="children")
    
    def __repr__(self):
        return f"<Site(id={self.id}, code='{self.code}', name='{self.name}')>" 