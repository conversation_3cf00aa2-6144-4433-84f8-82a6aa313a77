# Site Management Module

## Mô tả
Module Site Management cung cấp các tính năng quản lý thông tin và khai báo Site. Site là định danh của một tư cách pháp nhân của tổ chức.

## Cấu hình Module
- **Tên module**: `site_management`
- **Loại module**: Core Module (hiển thị trong tab Module hệ thống)
- **Tự động cài đặt**: Có
- **Ẩn khỏi AppsPage**: Không
- **Phiên bản**: 1.0
- **Tác giả**: Metis AI
- **Danh mục**: Core

## Cấu trúc Database

### Bảng Sites
| Trường | Kiểu dữ liệu | Mô tả | Ràng buộc |
|--------|-------------|-------|-----------|
| id | Integer | M<PERSON> số tăng tự động, khóa chính | Primary Key, Auto Increment |
| code | String(10) | Mã viết tắt của Site | Unique, Not Null, Index |
| name | String(250) | Tên của Site | Not Null |
| descriptions | String(1000) | Mô tả site | Nullable |
| is_activated | Boolean | Trạng thái kích hoạt | Default: True |
| parent_id | Integer | Mã ID Site cha | Foreign Key (sites.id), Nullable |

### Quan hệ
- **Self-referencing**: Sites có thể có quan hệ cha-con với chính nó
- **Parent-Child**: Một Site có thể có nhiều Site con

## API Endpoints

### Quản lý Sites
- `GET /api/site-management/sites` - Lấy danh sách tất cả Sites
- `GET /api/site-management/sites/{site_id}` - Lấy thông tin Site theo ID
- `POST /api/site-management/sites` - Tạo Site mới
- `PUT /api/site-management/sites/{site_id}` - Cập nhật thông tin Site
- `DELETE /api/site-management/sites/{site_id}` - Xóa Site

### Quan hệ Site
- `GET /api/site-management/sites/{site_id}/children` - Lấy danh sách Site con
- `GET /api/site-management/sites/{site_id}/parent` - Lấy thông tin Site cha

## Quy tắc nghiệp vụ

### Tạo Site
- Code phải là duy nhất
- Nếu có parent_id, Site cha phải tồn tại
- Mặc định is_activated = True

### Cập nhật Site
- Code mới không được trùng với Site khác
- Không thể set parent là chính nó
- Parent_id mới phải tồn tại

### Xóa Site
- Không thể xóa Site có Site con
- Core Module không thể gỡ cài đặt

## Migration

### Tạo bảng
```bash
cd backend
python migrate_site_management.py create
```

### Xóa bảng
```bash
cd backend
python migrate_site_management.py drop
```

## Dữ liệu mẫu
Module tự động tạo dữ liệu mẫu khi được cài đặt:
- **Trụ sở chính** (Code: HQ) - Site cha
- **Chi nhánh 1** (Code: BR1) - Site con của Trụ sở chính

## Frontend
Module có giao diện riêng tại `/site-management` với các tính năng:
- Tổng quan module
- Thống kê hệ thống
- Hoạt động gần đây
- Responsive design cho mobile và desktop 