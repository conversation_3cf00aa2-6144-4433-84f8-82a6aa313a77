# Đường dẫn: backend/src/modules/user_management/models.py
import uuid
from sqlalchemy import Column, String, Boolean, INT, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from ...core.database import Base # Import Base từ thư mục core

class Department(Base):
    __tablename__ = "departments"
    id = Column(INT, primary_key=True)
    name = Column(String(255), nullable=False)
    parent_id = Column(INT, ForeignKey('departments.id'))

class Role(Base):
    __tablename__ = "roles"
    id = Column(INT, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(String(500))

class User(Base):
    __tablename__ = "users"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(150), nullable=False)
    email = Column(String(150), unique=True, index=True, nullable=False)
    phone_number = Column(String(20), nullable=True)
    department_id = Column(INT, ForeignKey('departments.id'))
    is_active = Column(Boolean, default=True)

class UserRole(Base):
    __tablename__ = "user_roles"
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), primary_key=True)
    role_id = Column(INT, ForeignKey('roles.id'), primary_key=True)