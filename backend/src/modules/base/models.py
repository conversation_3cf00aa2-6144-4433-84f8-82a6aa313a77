# Đường dẫn: backend/src/modules/base/models.py
from sqlalchemy import Column, VARCHAR, ARRAY, Text
from ...core.database import Base # Import Base từ thư mục core

class IrModule(Base):
    __tablename__ = "ir_module"

    name = Column(VARCHAR(128), primary_key=True)
    state = Column(VARCHAR(16), default='uninstalled')
    version = Column(VARCHAR(32))
    summary = Column(VARCHAR(255))
    author = Column(VARCHAR(128))
    depends = Column(ARRAY(VARCHAR(128)), default=[])  # Danh sách module phụ thuộc
    description = Column(Text)  # Mô tả chi tiết của module
    category = Column(VARCHAR(64))  # Danh mục module (Core, Business, etc.)